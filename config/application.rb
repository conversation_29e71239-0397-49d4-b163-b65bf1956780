require_relative "boot"

require "rails/all"
# require "#{Rails.root}/app/inputs/lauir_radio_buttons_input.rb"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module SmartAlliance
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    config.log_level = :debug
    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    config.time_zone = 'Beijing'
    config.i18n.default_locale = 'zh-cn'
    config.i18n.load_path += Dir[Rails.root.join('config', 'locales', '**', '*.{rb,yml}')]
    config.eager_load_paths << Rails.root.join('lib')
    config.autoload_paths += %W[#{Rails.root}/lib]
    # config.active_job.queue_adapter = :sidekiq
    config.encoding = "utf-8"
  end
end
