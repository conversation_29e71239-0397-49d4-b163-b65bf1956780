zh-cn:
  attributes:
    created_at: 创建时间
    updated_at: 更新时间
  activemodel:
  activerecord:
    models:
      ai_chat: AI工具
      organization: 组织
      evaluate_config: 评价配置
      recharge_config: 充值配置
      project_role_config: 项目角色配置
      project: 项目
      chip_config: 芯片平台
      chip_os_version: 芯片版本
      product_category: 项目分类
      chip_os_software: 芯片软件
      work_order: 工单
      project_user: 项目用户
      role: 角色
      plan: 计划
      organization_flow: 审核流程
      flow_steps: 流程步骤
      work_order_reason_log: 工单原因履历
      project_organization: 项目参与组织
      project_risk: 项目风险
    attributes:
      project_role_config:
        role_name: 角色名称
        role_type: 角色类型
      organization:
        name: 企业名称
        org_type: 企业类型
        parent_id: 父级企业ID
      projects:
        acceptor_name: 业务受理人
        agreement_accepted: 是否同意协议
        chip_config_id: 芯片平台ID
        customer_project_name: 客户项目简称
        deleted_at: 删除时间
        description: 项目描述
        design_in_at: 立项时间
        dvt_at: 设计验证时间
        email: 邮箱
        evt_at: 工程测试时间
        fcst_per_month: FCST/月
        main_competitiveness: 产品主要功能
        main_purpose: 项目目标、主要目的
        mp_at: 批量生产时间
        mp_plus_six_months: MP+6个月
        name: 项目名称
        opening_at: 开案时间
        opening_desc: 开案说明
        os_version: OS系统版本
        phone: 联系电话
        product_name: 产品名称
        project_type: 项目类型
        pvt_at: 小批量过程验证时间
        specification_file: 规格附件
        status: 状态
        target_market_region: 目标市场区域
        team_size: 项目人数
        terminal_customer_name: 终端客户简称
        username: 姓名
      chip_config:
        name: 名称
        code: 芯片编码
        product_line: 产品线
        c_type: 芯片类型
        product_category_id: 产品分类
        status: 状态
        description: 描述
        organization_id: 企业
      chip_os_software:
        name: 名称
        organization_id: 企业
        chip_config_id: 芯片平台
      work_order:
        aasm_state: 状态
        customer_name: 客户名称
        demand_sources: 需求来源
        description: 描述
        ended_at: 结束时间
        file: 附件
        founder_email: 创建人邮箱
        founder_phone: 创建人电话
        hardware_version: 硬件版本
        is_platform_commonality: 是否是平台共性
        priority: 优先级
        problem_severity: 问题严重程度
        product_name: 产品名称
        project_progress: 项目进度
        repro_steps: 重现步骤
        started_at: 开始时间
        title: 标题
        which_module: 所属模块
        work_type: 工单类型
        chip_config_id: 芯片平台ID
        chip_os_software_id: OS系统软件ID
        chip_os_version_id: OS系统软件版本ID
        founder_id: 创建人ID
        product_category_id: 产品类型
        receiver_user_id: 评估人
        workable: 多态
        project_id: 项目
        obligation_user_id: 责任人
        rejection_reason: 驳回理由
        solution: 解决方案
        customer_confirmation: 客户确认
        act_started_at: 实际开始时间
        act_ended_at: 实际结束时间
        closed_at: 关闭时间

      role:
        name: 角色名称
        description: 角色描述
      plans:
        act_ended_at: 实际结束时间
        act_started_at: 实际开始时间
        content: 计划内容
        duty_user_id: 责任人
        parent_id: 父计划
        project_id: 项目ID
        status: 状态
        p_priority: 优先级
        p_type: 类型
        started_at: 计划开始时间
        user_id: 用户ID
        ended_at: 计划结束时间
        name: 计划名称
      organization_flows:
        description: 流程描述
        flow_type: 类型
        name: 流程名称
        user_id: 用户I
      work_order_reason_log:
        work_order_id: 工单ID
        reason: 原因
        reason_type: 原因类型
      project_risk:
        name: 风险名称
        description: 风险描述
        aasm_state: 状态
        act_ended_at: 实际结束时间
        act_started_at: 实际开始时间
        category: 风险类别
        close_reason: 关闭原因
        coping_strategy: 应对策略
        ended_at: 期望结束时间
        project_id: 项目
        risk_level: 风险等级
        risk_type: 风险类型
        started_at: 期望开始时间
        which_module: 所属模块
        action_plan: 行动计划
        nature: 风险性质
        problem_severity: 问题严重程度
        user_id: 创建人名称
        obligation_user_id: 责任人
        riskable_type: 多态类型
        riskable_id: 多态ID

