Dir[Rails.root.join('lib/simple_form/inputs/**/*.rb')].each { |f| require f }
Dir[Rails.root.join('lib/components/**/*.rb')].each { |f| require f }
# Use this setup block to configure all options available in SimpleForm.
SimpleForm.setup do |config|
  config.browser_validations = false
  config.wrappers :seven_form_line, tag: 'div', class: 'layui-form', error_class: :field_with_errors do |b|
    b.use :html5
    b.use :error, wrap_with: { tag: :div, class: :'form-error text-center layui-anim layui-anim-scale' }
    b.wrapper :tag => 'div', :class => 'layui-form-item' do |ba|
      ba.use :placeholder
      ba.use :label, class: 'layui-form-label'
      ba.wrapper :tag => 'div', :class => 'layui-input-block flex-item layui-input-wrap' do |bb|
        bb.use :input, class: 'layui-input'
        bb.wrapper :tag => 'div', :class => 'layui-input-suffix' do |bbb|
          bbb.use :unit, wrap_with: { tag: :i, class: :'layui-icon'}
        end
        bb.use :text_aux, wrap_with: { tag: :div, class: :'layui-form-mid layui-font-red' }
        bb.use :word_aux, wrap_with: { tag: :div, class: :'unit_word_aux' }
        bb.use :textunit, wrap_with: { tag: :div, class: :'layui-input-split layui-input-suffix' }
        bb.use :text_content, wrap_with: { tag: :div, class: :'layui-input-suffix' }
      end

      ba.optional :maxlength
      ba.optional :minlength
      ba.optional :pattern
      ba.optional :min_max
      ba.optional :readonly
    end

    b.use :hint,  wrap_with: { tag: :div, class: :'form-hint text-center' }

  end

  config.wrappers :seven_form_search, tag: 'div', class: 'layui-form', error_class: :field_with_errors do |b|
    b.use :html5
    b.use :error, wrap_with: { tag: :div, class: :'form-error text-center layui-anim layui-anim-scale' }
    b.wrapper :tag => 'div', :class => 'layui-form-item' do |ba|
      ba.use :placeholder
      ba.use :label, class: 'layui-form-label'
      ba.wrapper :tag => 'div', :class => 'layui-input-block2' do |bb|
        bb.use :input, class: 'layui-input'
      end

      ba.optional :maxlength
      ba.optional :minlength
      ba.optional :pattern
      ba.optional :min_max
      ba.optional :readonly
    end

    b.use :hint,  wrap_with: { tag: :div, class: :'form-hint text-center' }

  end

  config.wrappers :seven_form, tag: 'div', class: 'layui-form layui-form-pane', error_class: :field_with_errors do |b|
    b.use :html5
    b.use :error, wrap_with: { tag: :div, class: :'form-error text-center layui-anim layui-anim-scale' }
    b.wrapper :tag => 'div', :class => 'layui-form-item layui-form-text' do |ba|
      ba.use :placeholder
      ba.use :label, class: 'layui-form-label'
      ba.wrapper :tag => 'div', :class => 'layui-input-block flex-item layui-input-wrap' do |bb|
        bb.use :input, class: 'layui-input'
        bb.wrapper :tag => 'div', :class => 'layui-input-suffix' do |bbb|
          bbb.use :unit, wrap_with: { tag: :i, class: :'layui-icon layui-icon-more-vertical iconfont'}
        end
        bb.use :text_aux, wrap_with: { tag: :div, class: :'layui-form-mid layui-font-red' }
        bb.use :word_aux, wrap_with: { tag: :div, class: :'unit_word_aux' }
      end

      ba.optional :maxlength
      ba.optional :minlength
      ba.optional :pattern
      ba.optional :min_max
      ba.optional :readonly
    end

    b.use :hint,  wrap_with: { tag: :div, class: :'form-hint text-center' }

  end

  # config.wrappers :uplode_img, :tag => 'div', :error_class => 'error' do |b|
  #   b.use :html5
  #   b.wrapper :tag => 'div', :id => 'sort_image', :class => 'form-element form-element-inline row wy-form' do |ba|
  #     ba.use :label, class: 'form-label url optional'
  #     ba.wrapper :tag => 'div', :class => 'wy-up-image' do |bb|
  #       bb.wrapper :tag => 'div', :class => 'eop_frontend_input file form-elemen' do |bc|
  #         bc.use :input, :wrap_with => { :class => 'form-input file required' }
  #         bc.use :unit, wrap_with: { tag: :span, class: :'unit2 iconfont' }
  #       end
  #     end
  #   end
  #   b.use :hint,  :wrap_with => { tag: :div, class: :'form-hint text-right' }
  #   b.use :error, :wrap_with => { :tag => 'span', :class => 'help-inline' }
  # end

  # config.wrappers :custom_radio_buttons, tag: 'div', class: 'custom-radio-buttons' do |b|
  #   b.use :html5
  #   b.optional :readonly

  #   # 自定义生成 radio buttons 的 HTML 规则
  #   b.wrapper tag: 'div', class: 'custom-radio-buttons-list' do |ba|
  #     ba.use :label_input
  #   end
  #   b.use :error, wrap_with: { tag: 'span', class: 'help-block' }
  # end
end

SimpleForm::ErrorNotification.class_eval do
  def render
    if has_errors?
      template.content_tag(:div, class: 'panel panel-danger bg-danger') do
        template.content_tag(:div, class: 'panel-body') do
          template.content_tag(:h5) do
            template.content_tag(:i,nil,class: "iconfont icon-ashbin-fill-circle fa-fw sign") +
              template.content_tag(:strong,"错误!")
          end +
            template.content_tag(error_notification_tag, error_message, html_options)
        end
      end
    end
  end
end

SimpleForm::FormBuilder.map_type :layui_radio_buttons, to: LayuiRadioButtonsInput
