class AddEmailToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :email, :string, comment: "邮箱"
    add_column :users, :actived_at, :datetime, comment: "激活时间"
    add_column :users, :locked_at, :datetime, comment: "锁定时间"
    add_column :users, :is_admin, :boolean, default: false, comment: "是否管理员"
    add_column :users, :organization_id, :integer, comment: "组织ID"
    add_index :users, :organization_id
  end
end
