class CreateFlowSteps < ActiveRecord::Migration[7.0]
  def change
    create_table :flow_steps do |t|
      t.integer :flow_version_id, comment: '流程版本ID'
      t.integer :order_number, comment: '步骤顺序'
      t.string :name, comment: '步骤名称'
      t.integer :review_type, comment: '审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）'
      t.string :review_user_ids, comment: '审核人用户ID列表'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'

      t.timestamps
    end
    add_index :flow_steps, :flow_version_id
    add_index :flow_steps, :organization_id
  end
end
