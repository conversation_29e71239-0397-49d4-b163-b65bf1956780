class CreateFlowVersions < ActiveRecord::Migration[7.0]
  def change
    create_table :flow_versions do |t|
      t.integer :version, comment: '版本号'
      t.boolean :status, default: false, comment: '启用状态'
      t.integer :organization_flow_id, comment: '流程ID'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'
      t.integer :user_id, comment: '用户ID'

      t.timestamps
    end
    add_index :flow_versions, :organization_flow_id
    add_index :flow_versions, :organization_id
    add_index :flow_versions, :user_id
  end
end
