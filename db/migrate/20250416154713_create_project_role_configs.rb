class CreateProjectRoleConfigs < ActiveRecord::Migration[7.0]
  def change
    create_table :project_role_configs do |t|
      t.string :name, comment: '角色名称'
      t.boolean :is_default, default: false, comment: '是否默认角色'
      t.integer :organization_id, comment: '外键: 组织id'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :project_role_configs, :organization_id
  end
end
