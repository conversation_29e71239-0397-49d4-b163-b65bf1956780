class CreateWorkOrders < ActiveRecord::Migration[7.0]
  def change
    create_table :work_orders do |t|
      t.string :aasm_state, comment: "状态 ｜ 问题状态"
      t.references :workable, polymorphic: true, comment: "多态"
      t.integer :work_type, comment: "工单类型 ｜ 问题类型" #枚举
      t.string :title, comment: "标题"
      t.string :description, comment: "问题描述"
      t.string :customer_name, comment: "客户名称"
      t.integer :priority, comment: "优先级" #枚举
      t.string :product_name, comment: "产品名称"
      t.string :hardware_version, comment: "硬件版本号"
      t.string :file, comment: "文件"
      t.integer :receiver_user_id, comment: "接收人"
      t.string :which_module, comment: "所属模块"
      t.integer :problem_severity, comment: "问题严重性" #枚举
      t.boolean :is_platform_commonality, comment: "是否是平台共性"
      t.integer :chip_config_id, comment: "芯片平台ID"
      t.integer :chip_os_software_id, comment: "OS系统软件ID, 软件"
      t.integer :chip_os_version_id, comment: "OS系统软件版本ID, 软件版本"
      t.integer :product_category_id, comment: "产品类型ID"
      t.integer :demand_sources, comment: "需求来源" #枚举
      t.text :repro_steps, comment: "重现步骤"
      t.datetime :started_at, comment: "开始时间"
      t.datetime :expected_ended_at, comment: "期望结束时间"
      t.datetime :ended_at, comment: "结束时间"
      t.integer :founder_id, comment: "创建人"
      t.string :founder_email, comment: "创建邮箱"
      t.string :founder_phone, comment: "创建电话"
      t.integer :project_progress, comment: "项目进度" #枚举
      t.datetime :deleted_at, comment: "软删除、删除时间"


      t.timestamps
    end
    add_index :work_orders, :chip_config_id
    add_index :work_orders, :chip_os_version_id
    add_index :work_orders, :product_category_id
    add_index :work_orders, :receiver_user_id
  end
end
