class CreateProjectRolePermissions < ActiveRecord::Migration[7.0]
  def change
    create_table :project_role_permissions do |t|
      t.integer :project_permission_config_id, comment: '外键: 项目权限配置ID'
      t.integer :project_role_config_id, comment: '外键: 角色配置ID'
      t.integer :organization_id, comment: '外键: 组织id'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :project_role_permissions, :project_permission_config_id
    add_index :project_role_permissions, :project_role_config_id
    add_index :project_role_permissions, :organization_id
  end
end
