class CreateChipOsVersions < ActiveRecord::Migration[7.0]
  def change
    create_table :chip_os_versions do |t|
      t.string :version, comment: '版本号'
      t.integer :chip_os_software_id, comment: '软件ID'
      t.boolean :is_default, default: false, comment: '是否默认'
      t.datetime :deleted_at
      t.integer :organization_id

      t.timestamps
    end
    add_index :chip_os_versions, :chip_os_software_id
    add_index :chip_os_versions, :organization_id
  end
end
