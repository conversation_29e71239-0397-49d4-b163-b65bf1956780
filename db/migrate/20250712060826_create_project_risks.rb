class CreateProjectRisks < ActiveRecord::Migration[7.0]
  def change
    create_table :project_risks do |t|
      t.integer :project_id, comment: '项目ID'
      t.references :riskable, polymorphic: true, comment: '多态'
      t.string :name, comment: '风险名称'
      t.integer :risk_type, comment: '风险类型'
      t.string :which_module, comment: '所属模块'
      t.integer :aasm_state, comment: '状态'
      t.string :description, comment: '风险描述'
      t.integer :risk_level, comment: '风险等级'
      t.integer :nature, comment: '风险性质'
      t.integer :problem_severity, comment: '影响程度'
      t.integer :coping_strategy, comment: '应对策略'
      t.text :action_plan, comment: '行动计划'
      t.string :close_reason, comment: '关闭原因'
      t.datetime :started_at, comment: '开始时间'
      t.datetime :ended_at, comment: '结束时间'
      t.datetime :act_started_at, comment: '实际起始时间'
      t.datetime :act_ended_at, comment: '时间结束时间'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :obligation_user_id, comment: '责任人ID'
      t.integer :user_id, comment: '创建人ID'

      t.timestamps
    end
    add_index :project_risks, :project_id
    add_index :project_risks, :obligation_user_id
    add_index :project_risks, :user_id
  end
end
