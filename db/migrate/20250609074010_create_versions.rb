# This migration creates the `versions` table, the only schema PT requires.
# All other migrations PT provides are optional.
class CreateVersions < ActiveRecord::Migration[7.0]

  # The largest text column available in all supported RDBMS is
  # 1024^3 - 1 bytes, roughly one gibibyte.  We specify a size
  # so that MySQL will use `longtext` instead of `text`.  Otherwise,
  # when serializing very large objects, `text` might not be big enough.
  TEXT_BYTES = 1_073_741_823

  def change
    create_table :versions do |t|
      t.bigint   :whodunnit, comment: '用户ID'
      t.datetime :created_at, comment: '创建时间'
      t.datetime :updated_at, comment: '更新时间'
      t.bigint   :item_id, null: false, comment: '多态记录ID'
      t.string   :item_type, null: false, comment: '多态记录类型'
      t.string   :event, null: false, comment: '变动事件'
      t.json     :object, comment: '变动记录对象'
      t.json     :object_changes, comment: '变动记录：字段由什么变动为什么'
    end
    add_index :versions, %i[item_type item_id]
  end
end
