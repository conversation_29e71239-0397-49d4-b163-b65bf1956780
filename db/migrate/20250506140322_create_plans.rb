class CreatePlans < ActiveRecord::Migration[7.0]
  def change
    create_table :plans do |t|
      t.integer :order_number, comment: '序号'
      t.integer :level, comment: '等级'
      t.string :name, comment: '计划名称'
      t.string :content, comment: '计划内容'
      t.datetime :started_at, comment: '计划开始时间'
      t.datetime :ended_at, comment: '计划结束时间'
      t.datetime :act_started_at, comment: '实际开始时间'
      t.datetime :act_ended_at, comment: '实际结束时间'
      t.integer :status, comment: '状态'
      t.integer :p_type, comment: '类型'
      t.integer :p_priority, comment: '优先级'
      t.integer :parent_id, comment: '父计划ID'
      t.integer :project_id, comment: '项目ID'
      t.integer :user_id, comment: '用户ID'
      t.integer :duty_user_id, comment: '责任人ID'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'

      t.timestamps
    end
    add_index :plans, :project_id
    add_index :plans, :user_id
    add_index :plans, :duty_user_id
    add_index :plans, :organization_id
  end
end
