class CreateRolePermissions < ActiveRecord::Migration[7.0]
  def change
    create_table :role_permissions do |t|
      t.integer :role_id, comment: "角色ID"
      t.integer :permission_action_id, comment: "权限ID"
      t.integer :organization_id, comment: "组织ID"
      t.datetime :deleted_at, comment: "删除时间"

      t.timestamps
    end
    add_index :role_permissions, :role_id
    add_index :role_permissions, :permission_action_id
    add_index :role_permissions, :organization_id
    add_index :role_permissions, :deleted_at
  end
end
