class CreatePermissionActions < ActiveRecord::Migration[7.0]
  def change
    create_table :permission_actions do |t|
      t.string :name, comment: "动作名称"
      t.string :word, comment: "action"
      t.integer :order_number, comment: "排序"
      t.integer :organization_id, comment: "组织ID"
      t.integer :permission_controller_id, comment: "控制器ID"
      t.datetime :deleted_at, comment: "删除时间"

      t.timestamps
    end
    add_index :permission_actions, :organization_id
    add_index :permission_actions, :deleted_at
    add_index :permission_actions, :permission_controller_id
  end
end
