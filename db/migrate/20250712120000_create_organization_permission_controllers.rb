class CreateOrganizationPermissionControllers < ActiveRecord::Migration[7.0]
  def change
    create_table :organization_permission_controllers do |t|
      t.integer :organization_id, comment: "企业ID"
      t.integer :permission_controller_id, comment: "权限控制器ID"

      t.timestamps
    end

    add_index :organization_permission_controllers, :organization_id, name: 'index_org_perm_ctrl_on_org_id'
    add_index :organization_permission_controllers, :permission_controller_id, name: 'index_org_perm_ctrl_on_perm_ctrl_id'
    add_index :organization_permission_controllers, [:organization_id, :permission_controller_id],
              unique: true, name: 'index_org_perm_ctrl_on_org_id_and_perm_ctrl_id'
  end
end
