class CreateChipConfigs < ActiveRecord::Migration[7.0]
  def change
    create_table :chip_configs do |t|
      t.string :name, comment: '芯片配置名称'
      t.integer :product_line, comment: '产品线'
      t.integer :c_type, comment: '芯片类型'
      t.integer :product_category_id, comment: '产品类型ID'
      t.string :description, comment: '描述'
      t.string :code, comment: '芯片编码'
      t.boolean :status, default: true, comment: '状态'
      t.datetime :deleted_at
      t.integer :organization_id

      t.timestamps
    end
    add_index :chip_configs, :organization_id
  end
end
