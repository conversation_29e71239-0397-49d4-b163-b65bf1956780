class CreateApprovalFlows < ActiveRecord::Migration[7.0]
  def change
    create_table :approval_flows do |t|
      t.integer :organization_flow_id, comment: '流程ID'
      t.references :flowable, polymorphic: true, comment: "多态"
      t.integer :current_step_id, comment: '当前步骤ID'
      t.integer :status, comment: '状态(1: "进行中", 2: "已完成", 3: "驳回")'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'
      t.integer :user_id, comment: '用户ID、发起人ID'
      t.boolean :is_effect, comment: '是否生效'

      t.timestamps
    end
    add_index :approval_flows, :organization_flow_id
    add_index :approval_flows, :organization_id
    add_index :approval_flows, :user_id
  end
end
