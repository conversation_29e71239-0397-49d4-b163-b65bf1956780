class CreateProjects < ActiveRecord::Migration[7.0]
  def change
    create_table :projects do |t|
      t.string :name, comment: '项目名称'
      t.integer :chip_config_id, comment: '芯片平台ID'
      t.integer :project_type, comment: '项目类型'
      t.string :product_name, comment: '产品名称'
      t.integer :product_category_id, comment: '产品类型ID'
      t.text :description, comment: '项目描述'
      t.string :main_purpose, comment: '项目目标、主要目的'
      t.datetime :design_in_at, comment: '立项时间'
      t.datetime :evt_at, comment: '工程测试时间'
      t.datetime :dvt_at, comment: '设计验证时间'
      t.datetime :pvt_at, comment: '小批量过程验证时间'
      t.datetime :mp_at, comment: '批量生产时间'
      t.integer :status, comment: '状态'
      t.decimal :fcst_per_month, precision: 14, scale: 2, comment: 'FCST/月'
      t.decimal :mp_plus_six_months, precision: 14, scale: 2, comment: 'MP+6个月'
      t.string :specification_file, comment: '规格附件'
      t.integer :user_id, comment: '用户ID、项目创建人'
      t.datetime :deleted_at, comment: '删除时间'
      t.string :terminal_customer_name, comment: '终端客户简称'
      t.integer :chip_os_version_id, comment: 'OS系统软件版本ID'
      t.integer :chip_os_software_id, comment: 'OS系统软件ID'
      t.integer :team_size, comment: '项目人数'
      t.string :customer_project_name, comment: '客户项目简称'
      t.string :target_market_region, comment: '目标市场区域'
      t.boolean :agreement_accepted, comment: '是否同意协议'
      t.string :username, comment: '姓名'
      t.string :phone, comment: '联系电话'
      t.string :email, comment: '邮箱'
      t.string :acceptor_name, comment: '业务受理人'
      t.datetime :opening_at, comment: '开案时间'
      t.text :opening_desc, comment: '开案说明'
      t.string :main_competitiveness, comment: '产品主要功能'
      t.integer :product_manager_id, comment: '产品经理'
      t.integer :technical_manager_id, comment: '技术经理'
      t.integer :business_contact_id, comment: '业务负责人'

      t.timestamps
    end
    add_index :projects, :user_id
    add_index :projects, :chip_config_id
    add_index :projects, :product_category_id
    add_index :projects, :chip_os_version_id
    add_index :projects, :chip_os_software_id
  end
end
