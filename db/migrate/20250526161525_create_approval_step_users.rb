class CreateApprovalStepUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :approval_step_users do |t|
      t.integer :order_number, comment: '步骤顺序'
      t.integer :user_id, comment: '审核人用户ID'
      t.integer :approval_step_id, comment: '审核步骤ID'
      t.integer :status, comment: '状态(1: 等待中, 2: "进行中", 3: "已完成", 4: "驳回")'
      t.string :review_comment, comment: '审核意见'
      t.datetime :deleted_at, comment: '删除时间'
      t.datetime :operating_at, comment: '操作时间'
      t.integer :organization_id, comment: '组织ID'

      t.timestamps
    end
    add_index :approval_step_users, :user_id
    add_index :approval_step_users, :approval_step_id
    add_index :approval_step_users, :organization_id
  end
end
