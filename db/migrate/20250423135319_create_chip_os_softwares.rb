class CreateChipOsSoftwares < ActiveRecord::Migration[7.0]
  def change
    create_table :chip_os_softwares do |t|
      t.string :name, comment: '软件名称'
      t.integer :chip_config_id, comment: '芯片平台ID'
      t.integer :organization_id
      t.datetime :deleted_at

      t.timestamps
    end
    add_index :chip_os_softwares, :chip_config_id
    add_index :chip_os_softwares, :organization_id
  end
end
