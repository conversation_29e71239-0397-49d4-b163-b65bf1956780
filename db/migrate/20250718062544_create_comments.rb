class CreateComments < ActiveRecord::Migration[7.0]
  def change
    create_table :comments do |t|
      t.integer :user_id, comment: '用户ID'
      t.string :commentable_type, comment: '评论对象类型'
      t.integer :commentable_id, comment: '评论对象ID'
      t.integer :parent_id, comment: '父级ID'
      t.text :content, comment: '评论内容'
      t.integer :organization_id, comment: '组织ID'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :comments, [:commentable_type, :commentable_id]
    add_index :comments, :user_id
    add_index :comments, :organization_id
  end
end
