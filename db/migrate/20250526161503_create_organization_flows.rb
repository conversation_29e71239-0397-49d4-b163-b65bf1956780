class CreateOrganizationFlows < ActiveRecord::Migration[7.0]
  def change
    create_table :organization_flows do |t|
      t.string :name, comment: '流程名称'
      t.string :description, comment: '流程描述'
      t.integer :flow_type, comment: '使用类型'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'
      t.integer :user_id, comment: '用户ID'

      t.timestamps
    end
    add_index :organization_flows, :organization_id
    add_index :organization_flows, :user_id
  end
end
