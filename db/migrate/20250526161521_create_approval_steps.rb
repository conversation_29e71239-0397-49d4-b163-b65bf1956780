class CreateApprovalSteps < ActiveRecord::Migration[7.0]
  def change
    create_table :approval_steps do |t|
      t.integer :approval_flow_id, comment: '审核步骤ID'
      t.integer :order_number, comment: '步骤顺序'
      t.string :name, comment: '步骤名称'
      t.integer :review_type, comment: '审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）'
      t.integer :status, comment: '状态(1: 等待中, 2: "进行中", 3: "已完成", 4: "驳回")'
      t.datetime :deleted_at, comment: '删除时间'
      t.integer :organization_id, comment: '组织ID'

      t.timestamps
    end
    add_index :approval_steps, :approval_flow_id
    add_index :approval_steps, :organization_id
  end
end
