class CreatePermissionControllers < ActiveRecord::Migration[7.0]
  def change
    create_table :permission_controllers do |t|
      t.string :name, comment: "控制器名称"
      t.string :word, comment: "controller"
      t.integer :order_number, comment: "排序"
      t.integer :organization_id, comment: "组织ID"
      t.datetime :deleted_at, comment: "删除时间"

      t.timestamps
    end
    add_index :permission_controllers, :organization_id
    add_index :permission_controllers, :deleted_at
  end
end
