class CreateUserNotices < ActiveRecord::Migration[7.0]
  def change
    create_table :user_notices do |t|
      t.integer :user_id, comment: '用户id'
      t.string :name, comment: '标题'
      t.text :content, comment: '内容'
      t.integer :organization_id, comment: '组织id'
      t.references :noticeable, polymorphic: true, comment: "多态"
      t.datetime :deleted_at, comment: '删除时间'
      t.boolean :status, default: false, comment: '阅读状态'
      t.string :url, comment: '跳转链接'

      t.timestamps
    end
    add_index :user_notices, :user_id
    add_index :user_notices, :organization_id
  end
end
