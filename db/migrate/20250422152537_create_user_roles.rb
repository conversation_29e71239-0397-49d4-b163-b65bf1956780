class CreateUserRoles < ActiveRecord::Migration[7.0]
  def change
    create_table :user_roles do |t|
      t.integer :user_id, comment: "用户ID"
      t.integer :role_id, comment: "角色ID"
      t.integer :organization_id, comment: "组织ID"
      t.datetime :deleted_at, comment: "删除时间"

      t.timestamps
    end
    add_index :user_roles, :user_id
    add_index :user_roles, :role_id
    add_index :user_roles, :organization_id
    add_index :user_roles, :deleted_at
  end
end
