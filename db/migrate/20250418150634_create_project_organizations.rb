class CreateProjectOrganizations < ActiveRecord::Migration[7.0]
  def change
    create_table :project_organizations do |t|
      t.integer :project_id, comment: '项目ID'
      t.integer :organization_id, comment: '组织ID'
      t.integer :p_type, comment: '组织类型 1: 组织者 2: 成员'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :project_organizations, :project_id
    add_index :project_organizations, :organization_id
  end
end
