class CreateProjectUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :project_users do |t|
      t.integer :user_id, comment: '外键: 用户id'
      t.integer :project_id, comment: '外键: 项目id'
      t.integer :project_role_config_id, comment: '外键: 角色配置ID'
      t.integer :organization_id, comment: '外键: 组织id'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :project_users, :user_id
    add_index :project_users, :project_id
    add_index :project_users, :project_role_config_id
    add_index :project_users, :organization_id
  end
end
