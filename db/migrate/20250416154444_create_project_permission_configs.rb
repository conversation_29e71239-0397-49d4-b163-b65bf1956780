class CreateProjectPermissionConfigs < ActiveRecord::Migration[7.0]
  def change
    create_table :project_permission_configs do |t|
      t.integer :order_number, comment: '排序'
      t.string :key, limit: 200, comment: '权限key'
      t.string :name, comment: '权限名称'
      t.integer :parent_id, comment: '父级id'
      t.integer :organization_id, comment: '外键: 组织id'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :project_permission_configs, :organization_id
  end
end
