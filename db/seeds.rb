# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

org = Organization.find_or_create_by(
  name: 'xx主公司',
  org_type: :agent
)

admin_role = Role.find_or_create_by(
  name: '管理员',
  organization_id: org.id
)

`bundle exec rake generate_admin_auth`

permission_action = PermissionAction.find_by(
  word: 'index',
  organization_id: org.id
)

if User.find_by(phone: '13333333333')
  user = User.find_by(phone: '13333333333')
  user.update(is_admin: true, organization_id: org.id)
else
  user = User.new(
    name: '管理员',
    phone: '13333333333',
    password: '12345678',
    password_confirmation: '12345678',
    status: :active,
    is_admin: true,
    organization_id: org.id
  )
  user.save
end


RolePermission.find_or_create_by(
  role_id: admin_role.id,
  permission_action_id: permission_action.id,
  organization_id: org.id
)

UserRole.find_or_create_by(
  user_id: user.id,
  role_id: admin_role.id
)

# 创建产品分类
["SBC", "AI PC", "AI nas", "AI 机器人", "机械臂", "双目视觉", "鸿蒙平板", "工业网关"].each do |name|
  ProductCategory.find_or_create_by(organization_id: org.id, name: name)
end

# 创建芯片
chip_config = ChipConfig.find_or_create_by(
  name: 'K1-芯片',
  product_line: 'k_series',
  c_type: 'c_universal',
  product_category_id: ProductCategory.first.id,
  code: '00001',
  status: true,
  organization_id: org.id,
  description: 'ubuntu'
)
chip_os_software = chip_config.chip_os_softwares.find_or_create_by(name: '鸿蒙系统', organization_id: org.id)
chip_os_software2 = chip_config.chip_os_softwares.find_or_create_by(name: 'Linux系统', organization_id: org.id)
chip_os_software.chip_os_versions.find_or_create_by(version: 'V1.0.0', organization_id: org.id, is_default: false)
chip_os_software.chip_os_versions.find_or_create_by(version: 'V1.0.1', organization_id: org.id, is_default: true)
chip_os_software2.chip_os_versions.find_or_create_by(version: 'V1.0.0', organization_id: org.id, is_default: false)
chip_os_software2.chip_os_versions.find_or_create_by(version: 'V1.0.1', organization_id: org.id, is_default: true)

chip_config = ChipConfig.find_or_create_by(
  name: 'K2-芯片',
  product_line: 'k_series',
  c_type: 'c_universal',
  product_category_id: ProductCategory.first.id,
  code: '00002',
  status: true,
  organization_id: org.id,
  description: 'ubuntu'
)
chip_os_software = chip_config.chip_os_softwares.find_or_create_by(name: '鸿蒙系统', organization_id: org.id)
chip_os_software2 = chip_config.chip_os_softwares.find_or_create_by(name: 'Linux系统', organization_id: org.id)
chip_os_software.chip_os_versions.find_or_create_by(version: 'V1.0.0', organization_id: org.id, is_default: false)
chip_os_software.chip_os_versions.find_or_create_by(version: 'V1.0.1', organization_id: org.id, is_default: true)
chip_os_software2.chip_os_versions.find_or_create_by(version: 'V1.0.0', organization_id: org.id, is_default: false)
chip_os_software2.chip_os_versions.find_or_create_by(version: 'V1.0.1', organization_id: org.id, is_default: true)

# 生成项目权限
ProjectPermissionConfig.generate_permission(org.id)
