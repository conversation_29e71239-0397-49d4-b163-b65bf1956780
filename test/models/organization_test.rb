# == Schema Information
#
# Table name: organizations
#
#  id                                                :bigint           not null, primary key
#  name(企业名称)                                    :string
#  org_type(企业类型: 1: 代理商 2: 方案商 3: 品牌商) :integer
#  created_at                                        :datetime         not null
#  updated_at                                        :datetime         not null
#  parent_id(父级企业ID)                             :integer
#
# Indexes
#
#  index_organizations_on_parent_id  (parent_id)
#
require "test_helper"

class OrganizationTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
