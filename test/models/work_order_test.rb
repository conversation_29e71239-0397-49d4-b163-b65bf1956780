# == Schema Information
#
# Table name: work_orders
#
#  id                                             :bigint           not null, primary key
#  aasm_state(状态 ｜ 问题状态)                   :string
#  act_ended_at(实际结束时间)                     :datetime
#  act_started_at(实际开始时间)                   :datetime
#  closed_at(关闭时间)                            :datetime
#  customer_confirmation(客户确认)                :string
#  customer_name(客户名称)                        :string
#  deleted_at(软删除、删除时间)                   :datetime
#  demand_sources(需求来源)                       :integer
#  description(问题描述)                          :string
#  ended_at(结束时间)                             :datetime
#  file(文件)                                     :string
#  founder_email(创建邮箱)                        :string
#  founder_phone(创建电话)                        :string
#  hardware_version(硬件版本号)                   :string
#  is_platform_commonality(是否是平台共性)        :boolean
#  priority(优先级)                               :integer
#  problem_severity(问题严重性)                   :integer
#  product_name(产品名称)                         :string
#  project_progress(项目进度)                     :integer
#  rejection_reason(驳回理由)                     :string
#  repro_steps(重现步骤)                          :text
#  solution(解决方案)                             :string
#  started_at(开始时间)                           :datetime
#  title(标题)                                    :string
#  which_module(所属模块)                         :string
#  work_type(工单类型 ｜ 问题类型)                :integer
#  workable_type                                  :string
#  created_at                                     :datetime         not null
#  updated_at                                     :datetime         not null
#  chip_config_id(芯片平台ID)                     :integer
#  chip_os_software_id(OS系统软件ID, 软件)        :integer
#  chip_os_version_id(OS系统软件版本ID, 软件版本) :integer
#  founder_id(创建人)                             :integer
#  obligation_user_id(责任人ID)                   :integer
#  product_category_id(产品类型ID)                :integer
#  project_id(项目ID)                             :integer
#  receiver_user_id(接收人)                       :integer
#  workable_id(多态)                              :bigint
#
# Indexes
#
#  index_work_orders_on_chip_config_id       (chip_config_id)
#  index_work_orders_on_chip_os_version_id   (chip_os_version_id)
#  index_work_orders_on_obligation_user_id   (obligation_user_id)
#  index_work_orders_on_product_category_id  (product_category_id)
#  index_work_orders_on_project_id           (project_id)
#  index_work_orders_on_receiver_user_id     (receiver_user_id)
#  index_work_orders_on_workable             (workable_type,workable_id)
#
require "test_helper"

class WorkOrderTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
