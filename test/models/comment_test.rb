# == Schema Information
#
# Table name: comments
#
#  id                             :bigint           not null, primary key
#  commentable_type(评论对象类型) :string
#  content(评论内容)              :text
#  deleted_at(删除时间)           :datetime
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  commentable_id(评论对象ID)     :integer
#  organization_id(组织ID)        :integer
#  parent_id(父级ID)              :integer
#  user_id(用户ID)                :integer
#
# Indexes
#
#  index_comments_on_commentable_type_and_commentable_id  (commentable_type,commentable_id)
#  index_comments_on_organization_id                      (organization_id)
#  index_comments_on_user_id                              (user_id)
#
require "test_helper"

class CommentTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
