# == Schema Information
#
# Table name: approval_steps
#
#  id                                                                                                            :bigint           not null, primary key
#  deleted_at(删除时间)                                                                                          :datetime
#  name(步骤名称)                                                                                                :string
#  order_number(步骤顺序)                                                                                        :integer
#  review_type(审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）) :integer
#  created_at                                                                                                    :datetime         not null
#  updated_at                                                                                                    :datetime         not null
#  approval_flow_id(审核步骤ID)                                                                                  :integer
#  organization_id(组织ID)                                                                                       :integer
#
# Indexes
#
#  index_approval_steps_on_approval_flow_id  (approval_flow_id)
#  index_approval_steps_on_organization_id   (organization_id)
#
require "test_helper"

class ApprovalStepTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
