# == Schema Information
#
# Table name: project_users
#
#  id                                       :bigint           not null, primary key
#  deleted_at(删除时间)                     :datetime
#  created_at                               :datetime         not null
#  updated_at                               :datetime         not null
#  organization_id(外键: 组织id)            :integer
#  project_id(外键: 项目id)                 :integer
#  project_role_config_id(外键: 角色配置ID) :integer
#  user_id(外键: 用户id)                    :integer
#
# Indexes
#
#  index_project_users_on_organization_id         (organization_id)
#  index_project_users_on_project_id              (project_id)
#  index_project_users_on_project_role_config_id  (project_role_config_id)
#  index_project_users_on_user_id                 (user_id)
#
require "test_helper"

class ProjectUserTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
