# == Schema Information
#
# Table name: work_order_reason_logs
#
#  id                    :bigint           not null, primary key
#  deleted_at(删除时间)  :datetime
#  reason(理由)          :string
#  reason_type(理由类型) :integer
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  work_order_id(工单Id) :integer
#
# Indexes
#
#  index_work_order_reason_logs_on_work_order_id  (work_order_id)
#
require "test_helper"

class WorkOrderReasonLogTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
