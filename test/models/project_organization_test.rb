# == Schema Information
#
# Table name: project_organizations
#
#  id                                 :bigint           not null, primary key
#  deleted_at(删除时间)               :datetime
#  operated_at                        :datetime
#  p_type(组织类型 1: 组织者 2: 成员) :integer
#  status                             :integer
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  organization_id(组织ID)            :integer
#  project_id(项目ID)                 :integer
#
# Indexes
#
#  index_project_organizations_on_organization_id  (organization_id)
#  index_project_organizations_on_project_id       (project_id)
#
require "test_helper"

class ProjectOrganizationTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
