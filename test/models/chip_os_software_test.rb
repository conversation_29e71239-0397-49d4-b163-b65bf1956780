# == Schema Information
#
# Table name: chip_os_softwares
#
#  id                         :bigint           not null, primary key
#  deleted_at                 :datetime
#  name(软件名称)             :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  chip_config_id(芯片平台ID) :integer
#  organization_id            :integer
#
# Indexes
#
#  index_chip_os_softwares_on_chip_config_id   (chip_config_id)
#  index_chip_os_softwares_on_organization_id  (organization_id)
#
require "test_helper"

class ChipOsSoftwareTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
