# == Schema Information
#
# Table name: projects
#
#  id                                   :bigint           not null, primary key
#  acceptor_name(业务受理人)            :string
#  agreement_accepted(是否同意协议)     :boolean
#  customer_project_name(客户项目简称)  :string
#  deleted_at(删除时间)                 :datetime
#  description(项目描述)                :text
#  design_in_at(立项时间)               :datetime
#  dvt_at(设计验证时间)                 :datetime
#  email(邮箱)                          :string
#  evt_at(工程测试时间)                 :datetime
#  fcst_per_month(FCST/月)              :decimal(14, 2)
#  main_competitiveness(产品主要功能)   :string
#  main_purpose(项目目标、主要目的)     :string
#  mp_at(批量生产时间)                  :datetime
#  mp_plus_six_months(MP+6个月)         :decimal(14, 2)
#  name(项目名称)                       :string
#  opening_at(开案时间)                 :datetime
#  opening_desc(开案说明)               :text
#  phone(联系电话)                      :string
#  product_name(产品名称)               :string
#  project_type(项目类型)               :integer
#  pvt_at(小批量过程验证时间)           :datetime
#  specification_file(规格附件)         :string
#  status(状态)                         :integer
#  target_market_region(目标市场区域)   :string
#  team_size(项目人数)                  :integer
#  terminal_customer_name(终端客户简称) :string
#  username(姓名)                       :string
#  created_at                           :datetime         not null
#  updated_at                           :datetime         not null
#  business_contact_id(业务负责人)      :integer
#  chip_config_id(芯片平台ID)           :integer
#  chip_organization_id(芯片归属组织)   :integer
#  chip_os_software_id(OS系统软件ID)    :integer
#  chip_os_version_id(OS系统软件版本ID) :integer
#  product_category_id(产品类型ID)      :integer
#  product_manager_id(产品经理)         :integer
#  technical_manager_id(技术经理)       :integer
#  user_id(用户ID、项目创建人)          :integer
#
# Indexes
#
#  index_projects_on_chip_config_id        (chip_config_id)
#  index_projects_on_chip_organization_id  (chip_organization_id)
#  index_projects_on_chip_os_software_id   (chip_os_software_id)
#  index_projects_on_chip_os_version_id    (chip_os_version_id)
#  index_projects_on_product_category_id   (product_category_id)
#  index_projects_on_user_id               (user_id)
#
require "test_helper"

class ProjectTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
