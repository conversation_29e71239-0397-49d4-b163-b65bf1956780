# == Schema Information
#
# Table name: flow_versions
#
#  id                           :bigint           not null, primary key
#  deleted_at(删除时间)         :datetime
#  status(启用状态)             :boolean          default(FALSE)
#  version(版本号)              :integer
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  organization_flow_id(流程ID) :integer
#  organization_id(组织ID)      :integer
#  user_id(用户ID)              :integer
#
# Indexes
#
#  index_flow_versions_on_organization_flow_id  (organization_flow_id)
#  index_flow_versions_on_organization_id       (organization_id)
#  index_flow_versions_on_user_id               (user_id)
#
require "test_helper"

class FlowVersionTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
