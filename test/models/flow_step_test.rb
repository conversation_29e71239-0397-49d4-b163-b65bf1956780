# == Schema Information
#
# Table name: flow_steps
#
#  id                                                                                                            :bigint           not null, primary key
#  deleted_at(删除时间)                                                                                          :datetime
#  name(步骤名称)                                                                                                :string
#  order_number(步骤顺序)                                                                                        :integer
#  review_type(审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）) :integer
#  review_user_ids(审核人用户ID列表)                                                                             :string
#  created_at                                                                                                    :datetime         not null
#  updated_at                                                                                                    :datetime         not null
#  flow_version_id(流程版本ID)                                                                                   :integer
#  organization_id(组织ID)                                                                                       :integer
#
# Indexes
#
#  index_flow_steps_on_flow_version_id  (flow_version_id)
#  index_flow_steps_on_organization_id  (organization_id)
#
require "test_helper"

class FlowStepTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
