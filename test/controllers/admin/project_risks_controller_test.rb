require 'test_helper'

class Admin::ProjectRisksControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project_risk = project_risks(:one)
  end

  test "should get index" do
    get admin_project_risks_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_project_risk_url
    assert_response :success
  end

  test "should create project_risk" do
    assert_difference('ProjectRisk.count') do
      post admin_project_risks_url, params: { project_risk: { aasm_state: @project_risk.aasm_state, act_ended_at: @project_risk.act_ended_at, act_started_at: @project_risk.act_started_at, action_plan: @project_risk.action_plan, category: @project_risk.category, close_reason: @project_risk.close_reason, coping_strategy: @project_risk.coping_strategy, description: @project_risk.description, ended_at: @project_risk.ended_at, name: @project_risk.name, nature: @project_risk.nature, problem_severity: @project_risk.problem_severity, project_id: @project_risk.project_id, risk_level: @project_risk.risk_level, risk_type: @project_risk.risk_type, riskable_id: @project_risk.riskable_id, riskable_type: @project_risk.riskable_type, started_at: @project_risk.started_at, which_module: @project_risk.which_module } }
    end

    assert_redirected_to admin_project_risk_url(ProjectRisk.last)
  end

  test "should show project_risk" do
    get admin_project_risk_url(@project_risk)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_project_risk_url(@project_risk)
    assert_response :success
  end

  test "should update project_risk" do
    patch admin_project_risk_url(@project_risk), params: { project_risk: { aasm_state: @project_risk.aasm_state, act_ended_at: @project_risk.act_ended_at, act_started_at: @project_risk.act_started_at, action_plan: @project_risk.action_plan, category: @project_risk.category, close_reason: @project_risk.close_reason, coping_strategy: @project_risk.coping_strategy, description: @project_risk.description, ended_at: @project_risk.ended_at, name: @project_risk.name, nature: @project_risk.nature, problem_severity: @project_risk.problem_severity, project_id: @project_risk.project_id, risk_level: @project_risk.risk_level, risk_type: @project_risk.risk_type, riskable_id: @project_risk.riskable_id, riskable_type: @project_risk.riskable_type, started_at: @project_risk.started_at, which_module: @project_risk.which_module } }
    assert_redirected_to admin_project_risk_url(ProjectRisk.last)
  end

  test "should destroy project_risk" do
    assert_difference('ProjectRisk.count', -1) do
      delete admin_project_risk_url(@project_risk)
    end

    assert_redirected_to admin_project_risks_url
  end
end
