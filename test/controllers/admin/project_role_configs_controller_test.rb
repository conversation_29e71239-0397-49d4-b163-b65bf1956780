require 'test_helper'

class Admin::ProjectRoleConfigsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project_role_config = project_role_configs(:one)
  end

  test "should get index" do
    get admin_project_role_configs_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_project_role_config_url
    assert_response :success
  end

  test "should create project_role_config" do
    assert_difference('ProjectRoleConfig.count') do
      post admin_project_role_configs_url, params: { project_role_config: { is_default: @project_role_config.is_default, name: @project_role_config.name, organization_id: @project_role_config.organization_id } }
    end

    assert_redirected_to admin_project_role_config_url(ProjectRoleConfig.last)
  end

  test "should show project_role_config" do
    get admin_project_role_config_url(@project_role_config)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_project_role_config_url(@project_role_config)
    assert_response :success
  end

  test "should update project_role_config" do
    patch admin_project_role_config_url(@project_role_config), params: { project_role_config: { is_default: @project_role_config.is_default, name: @project_role_config.name, organization_id: @project_role_config.organization_id } }
    assert_redirected_to admin_project_role_config_url(ProjectRoleConfig.last)
  end

  test "should destroy project_role_config" do
    assert_difference('ProjectRoleConfig.count', -1) do
      delete admin_project_role_config_url(@project_role_config)
    end

    assert_redirected_to admin_project_role_configs_url
  end
end
