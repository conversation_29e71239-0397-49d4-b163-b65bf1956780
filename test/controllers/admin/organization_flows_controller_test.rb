require 'test_helper'

class Admin::OrganizationFlowsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @organization_flow = organization_flows(:one)
  end

  test "should get index" do
    get admin_organization_flows_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_organization_flow_url
    assert_response :success
  end

  test "should create organization_flow" do
    assert_difference('OrganizationFlow.count') do
      post admin_organization_flows_url, params: { organization_flow: { description: @organization_flow.description, flow_type: @organization_flow.flow_type, name: @organization_flow.name, organization_id: @organization_flow.organization_id, user_id: @organization_flow.user_id } }
    end

    assert_redirected_to admin_organization_flow_url(OrganizationFlow.last)
  end

  test "should show organization_flow" do
    get admin_organization_flow_url(@organization_flow)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_organization_flow_url(@organization_flow)
    assert_response :success
  end

  test "should update organization_flow" do
    patch admin_organization_flow_url(@organization_flow), params: { organization_flow: { description: @organization_flow.description, flow_type: @organization_flow.flow_type, name: @organization_flow.name, organization_id: @organization_flow.organization_id, user_id: @organization_flow.user_id } }
    assert_redirected_to admin_organization_flow_url(OrganizationFlow.last)
  end

  test "should destroy organization_flow" do
    assert_difference('OrganizationFlow.count', -1) do
      delete admin_organization_flow_url(@organization_flow)
    end

    assert_redirected_to admin_organization_flows_url
  end
end
