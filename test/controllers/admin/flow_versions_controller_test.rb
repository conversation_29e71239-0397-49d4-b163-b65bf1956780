require 'test_helper'

class Admin::FlowVersionsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @flow_version = flow_versions(:one)
  end

  test "should get index" do
    get admin_flow_versions_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_flow_version_url
    assert_response :success
  end

  test "should create flow_version" do
    assert_difference('FlowVersion.count') do
      post admin_flow_versions_url, params: { flow_version: { organization_flow_id: @flow_version.organization_flow_id, organization_id: @flow_version.organization_id, status: @flow_version.status, user_id: @flow_version.user_id, version: @flow_version.version } }
    end

    assert_redirected_to admin_flow_version_url(FlowVersion.last)
  end

  test "should show flow_version" do
    get admin_flow_version_url(@flow_version)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_flow_version_url(@flow_version)
    assert_response :success
  end

  test "should update flow_version" do
    patch admin_flow_version_url(@flow_version), params: { flow_version: { organization_flow_id: @flow_version.organization_flow_id, organization_id: @flow_version.organization_id, status: @flow_version.status, user_id: @flow_version.user_id, version: @flow_version.version } }
    assert_redirected_to admin_flow_version_url(FlowVersion.last)
  end

  test "should destroy flow_version" do
    assert_difference('FlowVersion.count', -1) do
      delete admin_flow_version_url(@flow_version)
    end

    assert_redirected_to admin_flow_versions_url
  end
end
