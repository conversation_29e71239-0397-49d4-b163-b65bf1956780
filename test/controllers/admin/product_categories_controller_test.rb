require 'test_helper'

class Admin::ProductCategoriesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @product_category = product_categories(:one)
  end

  test "should get index" do
    get admin_product_categories_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_product_category_url
    assert_response :success
  end

  test "should create product_category" do
    assert_difference('ProductCategory.count') do
      post admin_product_categories_url, params: { product_category: { name: @product_category.name, organization_id: @product_category.organization_id } }
    end

    assert_redirected_to admin_product_category_url(ProductCategory.last)
  end

  test "should show product_category" do
    get admin_product_category_url(@product_category)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_product_category_url(@product_category)
    assert_response :success
  end

  test "should update product_category" do
    patch admin_product_category_url(@product_category), params: { product_category: { name: @product_category.name, organization_id: @product_category.organization_id } }
    assert_redirected_to admin_product_category_url(ProductCategory.last)
  end

  test "should destroy product_category" do
    assert_difference('ProductCategory.count', -1) do
      delete admin_product_category_url(@product_category)
    end

    assert_redirected_to admin_product_categories_url
  end
end
