require 'test_helper'

class Admin::ProjectsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project = projects(:one)
  end

  test "should get index" do
    get admin_projects_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_project_url
    assert_response :success
  end

  test "should create project" do
    assert_difference('Project.count') do
      post admin_projects_url, params: { project: { acceptor_name: @project.acceptor_name, agreement_accepted: @project.agreement_accepted, chip_platform: @project.chip_platform, company_name: @project.company_name, company_type: @project.company_type, customer_project_name: @project.customer_project_name, deleted_at: @project.deleted_at, description: @project.description, design_in_at: @project.design_in_at, dvt_at: @project.dvt_at, email: @project.email, evt_at: @project.evt_at, fcst_per_month: @project.fcst_per_month, main_competitiveness: @project.main_competitiveness, main_purpose: @project.main_purpose, manpower: @project.manpower, mp_at: @project.mp_at, mp_plus_six_months: @project.mp_plus_six_months, name: @project.name, opening_at: @project.opening_at, opening_desc: @project.opening_desc, os_version: @project.os_version, phone: @project.phone, product_name: @project.product_name, project_type: @project.project_type, pvt_at: @project.pvt_at, specification_file: @project.specification_file, status: @project.status, target_market_region: @project.target_market_region, team_size: @project.team_size, terminal_customer_name: @project.terminal_customer_name, user_id: @project.user_id, username: @project.username } }
    end

    assert_redirected_to admin_project_url(Project.last)
  end

  test "should show project" do
    get admin_project_url(@project)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_project_url(@project)
    assert_response :success
  end

  test "should update project" do
    patch admin_project_url(@project), params: { project: { acceptor_name: @project.acceptor_name, agreement_accepted: @project.agreement_accepted, chip_platform: @project.chip_platform, company_name: @project.company_name, company_type: @project.company_type, customer_project_name: @project.customer_project_name, deleted_at: @project.deleted_at, description: @project.description, design_in_at: @project.design_in_at, dvt_at: @project.dvt_at, email: @project.email, evt_at: @project.evt_at, fcst_per_month: @project.fcst_per_month, main_competitiveness: @project.main_competitiveness, main_purpose: @project.main_purpose, manpower: @project.manpower, mp_at: @project.mp_at, mp_plus_six_months: @project.mp_plus_six_months, name: @project.name, opening_at: @project.opening_at, opening_desc: @project.opening_desc, os_version: @project.os_version, phone: @project.phone, product_name: @project.product_name, project_type: @project.project_type, pvt_at: @project.pvt_at, specification_file: @project.specification_file, status: @project.status, target_market_region: @project.target_market_region, team_size: @project.team_size, terminal_customer_name: @project.terminal_customer_name, user_id: @project.user_id, username: @project.username } }
    assert_redirected_to admin_project_url(Project.last)
  end

  test "should destroy project" do
    assert_difference('Project.count', -1) do
      delete admin_project_url(@project)
    end

    assert_redirected_to admin_projects_url
  end
end
