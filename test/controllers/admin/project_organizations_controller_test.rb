require 'test_helper'

class Admin::ProjectOrganizationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project_organization = project_organizations(:one)
  end

  test "should get index" do
    get admin_project_organizations_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_project_organization_url
    assert_response :success
  end

  test "should create project_organization" do
    assert_difference('ProjectOrganization.count') do
      post admin_project_organizations_url, params: { project_organization: { operated_at: @project_organization.operated_at, organization_id: @project_organization.organization_id, p_type: @project_organization.p_type, project_id: @project_organization.project_id, status: @project_organization.status } }
    end

    assert_redirected_to admin_project_organization_url(ProjectOrganization.last)
  end

  test "should show project_organization" do
    get admin_project_organization_url(@project_organization)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_project_organization_url(@project_organization)
    assert_response :success
  end

  test "should update project_organization" do
    patch admin_project_organization_url(@project_organization), params: { project_organization: { operated_at: @project_organization.operated_at, organization_id: @project_organization.organization_id, p_type: @project_organization.p_type, project_id: @project_organization.project_id, status: @project_organization.status } }
    assert_redirected_to admin_project_organization_url(ProjectOrganization.last)
  end

  test "should destroy project_organization" do
    assert_difference('ProjectOrganization.count', -1) do
      delete admin_project_organization_url(@project_organization)
    end

    assert_redirected_to admin_project_organizations_url
  end
end
