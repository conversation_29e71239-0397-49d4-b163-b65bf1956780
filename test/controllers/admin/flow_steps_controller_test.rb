require 'test_helper'

class Admin::FlowStepsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @flow_step = flow_steps(:one)
  end

  test "should get index" do
    get admin_flow_steps_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_flow_step_url
    assert_response :success
  end

  test "should create flow_step" do
    assert_difference('FlowStep.count') do
      post admin_flow_steps_url, params: { flow_step: { flow_version_id: @flow_step.flow_version_id, name: @flow_step.name, order_number: @flow_step.order_number, organization_id: @flow_step.organization_id, review_type: @flow_step.review_type, review_user_ids: @flow_step.review_user_ids } }
    end

    assert_redirected_to admin_flow_step_url(FlowStep.last)
  end

  test "should show flow_step" do
    get admin_flow_step_url(@flow_step)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_flow_step_url(@flow_step)
    assert_response :success
  end

  test "should update flow_step" do
    patch admin_flow_step_url(@flow_step), params: { flow_step: { flow_version_id: @flow_step.flow_version_id, name: @flow_step.name, order_number: @flow_step.order_number, organization_id: @flow_step.organization_id, review_type: @flow_step.review_type, review_user_ids: @flow_step.review_user_ids } }
    assert_redirected_to admin_flow_step_url(FlowStep.last)
  end

  test "should destroy flow_step" do
    assert_difference('FlowStep.count', -1) do
      delete admin_flow_step_url(@flow_step)
    end

    assert_redirected_to admin_flow_steps_url
  end
end
