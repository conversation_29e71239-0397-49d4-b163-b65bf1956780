require 'test_helper'

class Admin::ChipConfigsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @chip_config = chip_configs(:one)
  end

  test "should get index" do
    get admin_chip_configs_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_chip_config_url
    assert_response :success
  end

  test "should create chip_config" do
    assert_difference('ChipConfig.count') do
      post admin_chip_configs_url, params: { chip_config: { deleted_at: @chip_config.deleted_at, name: @chip_config.name, organization_id: @chip_config.organization_id } }
    end

    assert_redirected_to admin_chip_config_url(ChipConfig.last)
  end

  test "should show chip_config" do
    get admin_chip_config_url(@chip_config)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_chip_config_url(@chip_config)
    assert_response :success
  end

  test "should update chip_config" do
    patch admin_chip_config_url(@chip_config), params: { chip_config: { deleted_at: @chip_config.deleted_at, name: @chip_config.name, organization_id: @chip_config.organization_id } }
    assert_redirected_to admin_chip_config_url(ChipConfig.last)
  end

  test "should destroy chip_config" do
    assert_difference('ChipConfig.count', -1) do
      delete admin_chip_config_url(@chip_config)
    end

    assert_redirected_to admin_chip_configs_url
  end
end
