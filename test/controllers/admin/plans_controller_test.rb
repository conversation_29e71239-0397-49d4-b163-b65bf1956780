require 'test_helper'

class Admin::PlansControllerTest < ActionDispatch::IntegrationTest
  setup do
    @plan = plans(:one)
  end

  test "should get index" do
    get admin_plans_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_plan_url
    assert_response :success
  end

  test "should create plan" do
    assert_difference('Plan.count') do
      post admin_plans_url, params: { plan: { act_ended_at: @plan.act_ended_at, act_started_at: @plan.act_started_at, content: @plan.content, deleted_at: @plan.deleted_at, duty_user_id: @plan.duty_user_id, ended_at: @plan.ended_at, name: @plan.name, p_priority: @plan.p_priority, p_type: @plan.p_type, parent_id: @plan.parent_id, project_id: @plan.project_id, started_at: @plan.started_at, status: @plan.status, user_id: @plan.user_id } }
    end

    assert_redirected_to admin_plan_url(Plan.last)
  end

  test "should show plan" do
    get admin_plan_url(@plan)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_plan_url(@plan)
    assert_response :success
  end

  test "should update plan" do
    patch admin_plan_url(@plan), params: { plan: { act_ended_at: @plan.act_ended_at, act_started_at: @plan.act_started_at, content: @plan.content, deleted_at: @plan.deleted_at, duty_user_id: @plan.duty_user_id, ended_at: @plan.ended_at, name: @plan.name, p_priority: @plan.p_priority, p_type: @plan.p_type, parent_id: @plan.parent_id, project_id: @plan.project_id, started_at: @plan.started_at, status: @plan.status, user_id: @plan.user_id } }
    assert_redirected_to admin_plan_url(Plan.last)
  end

  test "should destroy plan" do
    assert_difference('Plan.count', -1) do
      delete admin_plan_url(@plan)
    end

    assert_redirected_to admin_plans_url
  end
end
