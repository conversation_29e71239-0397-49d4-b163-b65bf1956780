require 'test_helper'

class Admin::WorkOrdersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @work_order = work_orders(:one)
  end

  test "should get index" do
    get admin_work_orders_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_work_order_url
    assert_response :success
  end

  test "should create work_order" do
    assert_difference('WorkOrder.count') do
      post admin_work_orders_url, params: { work_order: { aasm_state: @work_order.aasm_state, chip_config_id: @work_order.chip_config_id, chip_os_software_id: @work_order.chip_os_software_id, chip_os_version_id: @work_order.chip_os_version_id, customer_name: @work_order.customer_name, demand_sources: @work_order.demand_sources, description: @work_order.description, ended_at: @work_order.ended_at, file: @work_order.file, founder_email: @work_order.founder_email, founder_id: @work_order.founder_id, founder_phone: @work_order.founder_phone, hardware_version: @work_order.hardware_version, is_platform_commonality: @work_order.is_platform_commonality, priority: @work_order.priority, problem_severity: @work_order.problem_severity, product_category_id: @work_order.product_category_id, product_name: @work_order.product_name, project_progress: @work_order.project_progress, receiver_user_id: @work_order.receiver_user_id, repro_steps: @work_order.repro_steps, started_at: @work_order.started_at, title: @work_order.title, which_module: @work_order.which_module, work_type: @work_order.work_type, workable_id: @work_order.workable_id, workable_type: @work_order.workable_type } }
    end

    assert_redirected_to admin_work_order_url(WorkOrder.last)
  end

  test "should show work_order" do
    get admin_work_order_url(@work_order)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_work_order_url(@work_order)
    assert_response :success
  end

  test "should update work_order" do
    patch admin_work_order_url(@work_order), params: { work_order: { aasm_state: @work_order.aasm_state, chip_config_id: @work_order.chip_config_id, chip_os_software_id: @work_order.chip_os_software_id, chip_os_version_id: @work_order.chip_os_version_id, customer_name: @work_order.customer_name, demand_sources: @work_order.demand_sources, description: @work_order.description, ended_at: @work_order.ended_at, file: @work_order.file, founder_email: @work_order.founder_email, founder_id: @work_order.founder_id, founder_phone: @work_order.founder_phone, hardware_version: @work_order.hardware_version, is_platform_commonality: @work_order.is_platform_commonality, priority: @work_order.priority, problem_severity: @work_order.problem_severity, product_category_id: @work_order.product_category_id, product_name: @work_order.product_name, project_progress: @work_order.project_progress, receiver_user_id: @work_order.receiver_user_id, repro_steps: @work_order.repro_steps, started_at: @work_order.started_at, title: @work_order.title, which_module: @work_order.which_module, work_type: @work_order.work_type, workable_id: @work_order.workable_id, workable_type: @work_order.workable_type } }
    assert_redirected_to admin_work_order_url(WorkOrder.last)
  end

  test "should destroy work_order" do
    assert_difference('WorkOrder.count', -1) do
      delete admin_work_order_url(@work_order)
    end

    assert_redirected_to admin_work_orders_url
  end
end
