require 'test_helper'

class Admin::ProjectUsersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project_user = project_users(:one)
  end

  test "should get index" do
    get admin_project_users_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_project_user_url
    assert_response :success
  end

  test "should create project_user" do
    assert_difference('ProjectUser.count') do
      post admin_project_users_url, params: { project_user: { organization_id: @project_user.organization_id, project_id: @project_user.project_id, project_role_config_id: @project_user.project_role_config_id, user_id: @project_user.user_id } }
    end

    assert_redirected_to admin_project_user_url(ProjectUser.last)
  end

  test "should show project_user" do
    get admin_project_user_url(@project_user)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_project_user_url(@project_user)
    assert_response :success
  end

  test "should update project_user" do
    patch admin_project_user_url(@project_user), params: { project_user: { organization_id: @project_user.organization_id, project_id: @project_user.project_id, project_role_config_id: @project_user.project_role_config_id, user_id: @project_user.user_id } }
    assert_redirected_to admin_project_user_url(ProjectUser.last)
  end

  test "should destroy project_user" do
    assert_difference('ProjectUser.count', -1) do
      delete admin_project_user_url(@project_user)
    end

    assert_redirected_to admin_project_users_url
  end
end
