require 'test_helper'

class Admin::ChipOsVersionsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @chip_os_version = chip_os_versions(:one)
  end

  test "should get index" do
    get admin_chip_os_versions_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_chip_os_version_url
    assert_response :success
  end

  test "should create chip_os_version" do
    assert_difference('ChipOsVersion.count') do
      post admin_chip_os_versions_url, params: { chip_os_version: { chip_config_id: @chip_os_version.chip_config_id, is_default: @chip_os_version.is_default, organization_id: @chip_os_version.organization_id, version: @chip_os_version.version } }
    end

    assert_redirected_to admin_chip_os_version_url(ChipOsVersion.last)
  end

  test "should show chip_os_version" do
    get admin_chip_os_version_url(@chip_os_version)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_chip_os_version_url(@chip_os_version)
    assert_response :success
  end

  test "should update chip_os_version" do
    patch admin_chip_os_version_url(@chip_os_version), params: { chip_os_version: { chip_config_id: @chip_os_version.chip_config_id, is_default: @chip_os_version.is_default, organization_id: @chip_os_version.organization_id, version: @chip_os_version.version } }
    assert_redirected_to admin_chip_os_version_url(ChipOsVersion.last)
  end

  test "should destroy chip_os_version" do
    assert_difference('ChipOsVersion.count', -1) do
      delete admin_chip_os_version_url(@chip_os_version)
    end

    assert_redirected_to admin_chip_os_versions_url
  end
end
