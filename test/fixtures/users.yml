# == Schema Information
#
# Table name: users
#
#  id                      :bigint           not null, primary key
#  actived_at(激活时间)    :datetime
#  avatar                  :string(200)
#  deleted_at              :datetime
#  email(邮箱)             :string
#  is_admin(是否管理员)    :boolean          default(FALSE)
#  last_login_at           :datetime
#  locked_at(锁定时间)     :datetime
#  name                    :string(200)
#  password_digest         :string
#  phone                   :string(200)
#  status                  :integer
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  feishu_user_id          :string
#  organization_id(组织ID) :integer
#  recommend_user_id       :integer
#
# Indexes
#
#  index_users_on_feishu_user_id   (feishu_user_id) UNIQUE
#  index_users_on_organization_id  (organization_id)
#

one:
  name: MyString
  phone: MyString
  password_digest: MyString
  avatar: MyString
  invite_code: MyString
  recommend_user_id: 1
  deleted_at: 2023-07-17 09:18:00

two:
  name: MyString
  phone: MyString
  password_digest: MyString
  avatar: MyString
  invite_code: MyString
  recommend_user_id: 1
  deleted_at: 2023-07-17 09:18:00
