# == Schema Information
#
# Table name: user_notices
#
#  id                      :bigint           not null, primary key
#  content(内容)           :text
#  deleted_at(删除时间)    :datetime
#  name(标题)              :string
#  noticeable_type         :string
#  status(阅读状态)        :boolean          default(FALSE)
#  url(跳转链接)           :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  noticeable_id(多态)     :bigint
#  organization_id(组织id) :integer
#  user_id(用户id)         :integer
#
# Indexes
#
#  index_user_notices_on_noticeable       (noticeable_type,noticeable_id)
#  index_user_notices_on_organization_id  (organization_id)
#  index_user_notices_on_user_id          (user_id)
#

one:
  user_id: 1
  name: MyString
  content: MyText
  organization_id: 1
  deleted_at: 2025-06-10 23:34:50
  status: false
  url: MyString

two:
  user_id: 1
  name: MyString
  content: MyText
  organization_id: 1
  deleted_at: 2025-06-10 23:34:50
  status: false
  url: MyString
