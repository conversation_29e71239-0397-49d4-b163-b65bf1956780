# == Schema Information
#
# Table name: project_role_configs
#
#  id                            :bigint           not null, primary key
#  deleted_at(删除时间)          :datetime
#  is_default(是否默认角色)      :boolean          default(FALSE)
#  name(角色名称)                :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  organization_id(外键: 组织id) :integer
#
# Indexes
#
#  index_project_role_configs_on_organization_id  (organization_id)
#

one:
  name: MyString
  is_default: false
  organization_id: 1
  deleted_at: 2025-04-16 23:47:13

two:
  name: MyString
  is_default: false
  organization_id: 1
  deleted_at: 2025-04-16 23:47:13
