# == Schema Information
#
# Table name: organization_flows
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  description(流程描述)   :string
#  flow_type(使用类型)     :integer
#  name(流程名称)          :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#  user_id(用户ID)         :integer
#
# Indexes
#
#  index_organization_flows_on_organization_id  (organization_id)
#  index_organization_flows_on_user_id          (user_id)
#

one:
  name: MyString
  description: MyString
  flow_type: 1
  deleted_at: 2025-05-27 00:15:03
  organization_id: 1
  user_id: 1

two:
  name: MyString
  description: MyString
  flow_type: 1
  deleted_at: 2025-05-27 00:15:03
  organization_id: 1
  user_id: 1
