# == Schema Information
#
# Table name: plans
#
#  id                           :bigint           not null, primary key
#  act_ended_at(实际结束时间)   :datetime
#  act_started_at(实际开始时间) :datetime
#  content(计划内容)            :string
#  deleted_at(删除时间)         :datetime
#  ended_at(计划结束时间)       :datetime
#  name(计划名称)               :string
#  p_priority(优先级)           :integer
#  p_type(类型)                 :integer
#  started_at(计划开始时间)     :datetime
#  status(状态)                 :integer
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  duty_user_id(责任人ID)       :integer
#  organization_id(组织ID)      :integer
#  parent_id(父计划ID)          :integer
#  project_id(项目ID)           :integer
#  user_id(用户ID)              :integer
#
# Indexes
#
#  index_plans_on_duty_user_id     (duty_user_id)
#  index_plans_on_organization_id  (organization_id)
#  index_plans_on_project_id       (project_id)
#  index_plans_on_user_id          (user_id)
#

one:
  name: MyString
  content: MyString
  started_at: 2025-05-06 22:03:22
  ended_at: 2025-05-06 22:03:22
  act_started_at: 2025-05-06 22:03:22
  act_ended_at: 2025-05-06 22:03:22
  status: 1
  p_type: 1
  p_priority: 1
  parent_id: 1
  project_id: 1
  user_id: 1
  duty_user_id: 1
  deleted_at: 2025-05-06 22:03:22

two:
  name: MyString
  content: MyString
  started_at: 2025-05-06 22:03:22
  ended_at: 2025-05-06 22:03:22
  act_started_at: 2025-05-06 22:03:22
  act_ended_at: 2025-05-06 22:03:22
  status: 1
  p_type: 1
  p_priority: 1
  parent_id: 1
  project_id: 1
  user_id: 1
  duty_user_id: 1
  deleted_at: 2025-05-06 22:03:22
