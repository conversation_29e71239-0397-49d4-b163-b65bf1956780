# == Schema Information
#
# Table name: chip_configs
#
#  id                              :bigint           not null, primary key
#  c_type(芯片类型)                :integer
#  code(芯片编码)                  :string
#  deleted_at                      :datetime
#  description(描述)               :string
#  name(芯片配置名称)              :string
#  product_line(产品线)            :integer
#  status(状态)                    :boolean          default(TRUE)
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  organization_id                 :integer
#  product_category_id(产品类型ID) :integer
#
# Indexes
#
#  index_chip_configs_on_organization_id  (organization_id)
#

one:
  name: MyString
  deleted_at: 2025-04-20 15:09:57
  organization_id: 1

two:
  name: MyString
  deleted_at: 2025-04-20 15:09:57
  organization_id: 1
