# == Schema Information
#
# Table name: roles
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  description(角色描述)   :text
#  name(角色名称)          :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#
# Indexes
#
#  index_roles_on_deleted_at       (deleted_at)
#  index_roles_on_organization_id  (organization_id)
#

one:
  name: MyString
  description: MyText
  organization_id: 1
  deleted_at: 2025-04-22 23:24:26

two:
  name: MyString
  description: MyText
  organization_id: 1
  deleted_at: 2025-04-22 23:24:26
