# == Schema Information
#
# Table name: approval_flows
#
#  id                                                :bigint           not null, primary key
#  deleted_at(删除时间)                              :datetime
#  flowable_type                                     :string
#  is_effect(是否生效)                               :boolean
#  status(状态(1: "进行中", 2: "已完成", 3: "驳回")) :integer
#  created_at                                        :datetime         not null
#  updated_at                                        :datetime         not null
#  current_step_id(当前步骤ID)                       :integer
#  flowable_id(多态)                                 :bigint
#  organization_flow_id(流程ID)                      :integer
#  organization_id(组织ID)                           :integer
#  user_id(用户ID、发起人ID)                         :integer
#
# Indexes
#
#  index_approval_flows_on_flowable              (flowable_type,flowable_id)
#  index_approval_flows_on_organization_flow_id  (organization_flow_id)
#  index_approval_flows_on_organization_id       (organization_id)
#  index_approval_flows_on_user_id               (user_id)
#

one:
  organization_flow_id: 1
  flowable_id: 1
  flowable_type: MyString
  current_step_id: 1
  status: 1
  deleted_at: 2025-05-27 00:15:17
  organization_id: 1

two:
  organization_flow_id: 1
  flowable_id: 1
  flowable_type: MyString
  current_step_id: 1
  status: 1
  deleted_at: 2025-05-27 00:15:17
  organization_id: 1
