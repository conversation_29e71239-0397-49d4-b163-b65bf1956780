# == Schema Information
#
# Table name: flow_steps
#
#  id                                                                                                            :bigint           not null, primary key
#  deleted_at(删除时间)                                                                                          :datetime
#  name(步骤名称)                                                                                                :string
#  order_number(步骤顺序)                                                                                        :integer
#  review_type(审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）) :integer
#  review_user_ids(审核人用户ID列表)                                                                             :string
#  created_at                                                                                                    :datetime         not null
#  updated_at                                                                                                    :datetime         not null
#  flow_version_id(流程版本ID)                                                                                   :integer
#  organization_id(组织ID)                                                                                       :integer
#
# Indexes
#
#  index_flow_steps_on_flow_version_id  (flow_version_id)
#  index_flow_steps_on_organization_id  (organization_id)
#

one:
  flow_version_id: 1
  order_number: 1
  name: MyString
  review_type: 1
  review_user_ids: MyString
  deleted_at: 2025-05-27 00:15:13
  organization_id: 1

two:
  flow_version_id: 1
  order_number: 1
  name: MyString
  review_type: 1
  review_user_ids: MyString
  deleted_at: 2025-05-27 00:15:13
  organization_id: 1
