# == Schema Information
#
# Table name: comments
#
#  id                             :bigint           not null, primary key
#  commentable_type(评论对象类型) :string
#  content(评论内容)              :text
#  deleted_at(删除时间)           :datetime
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  commentable_id(评论对象ID)     :integer
#  organization_id(组织ID)        :integer
#  parent_id(父级ID)              :integer
#  user_id(用户ID)                :integer
#
# Indexes
#
#  index_comments_on_commentable_type_and_commentable_id  (commentable_type,commentable_id)
#  index_comments_on_organization_id                      (organization_id)
#  index_comments_on_user_id                              (user_id)
#

one:
  user_id: 1
  commentable_type: MyString
  commentable_id: 1
  parent_id: 1
  content: MyText
  organization_id: 1
  deleted_at: 2025-07-18 14:25:44

two:
  user_id: 1
  commentable_type: MyString
  commentable_id: 1
  parent_id: 1
  content: MyText
  organization_id: 1
  deleted_at: 2025-07-18 14:25:44
