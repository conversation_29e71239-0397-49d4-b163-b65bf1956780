# == Schema Information
#
# Table name: chip_os_versions
#
#  id                          :bigint           not null, primary key
#  deleted_at                  :datetime
#  is_default(是否默认)        :boolean          default(FALSE)
#  version(版本号)             :string
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  chip_os_software_id(软件ID) :integer
#  organization_id             :integer
#
# Indexes
#
#  index_chip_os_versions_on_chip_os_software_id  (chip_os_software_id)
#  index_chip_os_versions_on_organization_id      (organization_id)
#

one:
  version: MyString
  chip_config_id: 1
  deleted_at: 2025-04-20 15:10:28
  organization_id: 1

two:
  version: MyString
  chip_config_id: 1
  deleted_at: 2025-04-20 15:10:28
  organization_id: 1
