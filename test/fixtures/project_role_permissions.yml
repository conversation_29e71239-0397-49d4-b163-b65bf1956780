# == Schema Information
#
# Table name: project_role_permissions
#
#  id                                                 :bigint           not null, primary key
#  deleted_at(删除时间)                               :datetime
#  created_at                                         :datetime         not null
#  updated_at                                         :datetime         not null
#  organization_id(外键: 组织id)                      :integer
#  project_permission_config_id(外键: 项目权限配置ID) :integer
#  project_role_config_id(外键: 角色配置ID)           :integer
#
# Indexes
#
#  index_project_role_permissions_on_organization_id               (organization_id)
#  index_project_role_permissions_on_project_permission_config_id  (project_permission_config_id)
#  index_project_role_permissions_on_project_role_config_id        (project_role_config_id)
#

one:
  project_permission_config_id: 1
  project_role_config_id: 1
  organization_id: 1
  deleted_at: 2025-04-16 23:49:00

two:
  project_permission_config_id: 1
  project_role_config_id: 1
  organization_id: 1
  deleted_at: 2025-04-16 23:49:00
