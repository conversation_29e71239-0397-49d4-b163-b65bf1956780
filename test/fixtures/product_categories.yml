# == Schema Information
#
# Table name: product_categories
#
#  id              :bigint           not null, primary key
#  deleted_at      :datetime
#  name            :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organization_id :integer
#
# Indexes
#
#  index_product_categories_on_organization_id  (organization_id)
#

one:
  name: MyString
  organization_id: 1
  deleted_at: 2025-04-20 15:12:23

two:
  name: MyString
  organization_id: 1
  deleted_at: 2025-04-20 15:12:23
