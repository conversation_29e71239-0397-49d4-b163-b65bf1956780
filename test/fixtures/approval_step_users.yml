# == Schema Information
#
# Table name: approval_step_users
#
#  id                                                           :bigint           not null, primary key
#  deleted_at(删除时间)                                         :datetime
#  order_number(步骤顺序)                                       :integer
#  review_comment(审核意见)                                     :string
#  status(状态(1: 等待中, 2: "进行中", 3: "已完成", 4: "驳回")) :integer
#  created_at                                                   :datetime         not null
#  updated_at                                                   :datetime         not null
#  approval_step_id(审核步骤ID)                                 :integer
#  organization_id(组织ID)                                      :integer
#  user_id(审核人用户ID)                                        :integer
#
# Indexes
#
#  index_approval_step_users_on_approval_step_id  (approval_step_id)
#  index_approval_step_users_on_organization_id   (organization_id)
#  index_approval_step_users_on_user_id           (user_id)
#

one:
  order_number: 1
  user_id: 1
  approval_step_id: 1
  status: 1
  comment: MyString
  deleted_at: 2025-05-27 00:15:25
  organization_id: 1

two:
  order_number: 1
  user_id: 1
  approval_step_id: 1
  status: 1
  comment: MyString
  deleted_at: 2025-05-27 00:15:25
  organization_id: 1
