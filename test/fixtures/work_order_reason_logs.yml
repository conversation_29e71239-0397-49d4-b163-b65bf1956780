# == Schema Information
#
# Table name: work_order_reason_logs
#
#  id                    :bigint           not null, primary key
#  deleted_at(删除时间)  :datetime
#  reason(理由)          :string
#  reason_type(理由类型) :integer
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  work_order_id(工单Id) :integer
#
# Indexes
#
#  index_work_order_reason_logs_on_work_order_id  (work_order_id)
#

one:
  work_order_id: 1
  reason: MyString
  reason_type: 1
  deleted_at: 2025-06-15 16:58:59

two:
  work_order_id: 1
  reason: MyString
  reason_type: 1
  deleted_at: 2025-06-15 16:58:59
