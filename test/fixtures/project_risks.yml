# == Schema Information
#
# Table name: project_risks
#
#  id                           :bigint           not null, primary key
#  aasm_state(状态)             :integer
#  act_ended_at(时间结束时间)   :datetime
#  act_started_at(实际起始时间) :datetime
#  action_plan(行动计划)        :text
#  close_reason(关闭原因)       :string
#  coping_strategy(应对策略)    :integer
#  deleted_at(删除时间)         :datetime
#  description(风险描述)        :string
#  ended_at(结束时间)           :datetime
#  name(风险名称)               :string
#  nature(风险性质)             :integer
#  problem_severity(影响程度)   :integer
#  risk_level(风险等级)         :integer
#  risk_type(风险类型)          :integer
#  riskable_type                :string
#  started_at(开始时间)         :datetime
#  which_module(所属模块)       :string
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  obligation_user_id(责任人ID) :integer
#  project_id(项目ID)           :integer
#  riskable_id(多态)            :bigint
#  user_id(创建人ID)            :integer
#
# Indexes
#
#  index_project_risks_on_obligation_user_id  (obligation_user_id)
#  index_project_risks_on_project_id          (project_id)
#  index_project_risks_on_riskable            (riskable_type,riskable_id)
#  index_project_risks_on_user_id             (user_id)
#

one:
  project_id: 1
  riskable: one
  riskable_type: Riskable
  category: MyString
  name: MyString
  risk_type: MyString
  which_module: MyString
  aasm_state: 1
  description: MyString
  risk_level: 1
  nature: 1
  problem_severity: 1
  coping_strategy: 1
  action_plan: MyText
  close_reason: MyString
  started_at: 2025-07-12 14:08:26
  ended_at: 2025-07-12 14:08:26
  act_started_at: 2025-07-12 14:08:26
  act_ended_at: 2025-07-12 14:08:26
  organization_id: 1

two:
  project_id: 1
  riskable: two
  riskable_type: Riskable
  category: MyString
  name: MyString
  risk_type: MyString
  which_module: MyString
  aasm_state: 1
  description: MyString
  risk_level: 1
  nature: 1
  problem_severity: 1
  coping_strategy: 1
  action_plan: MyText
  close_reason: MyString
  started_at: 2025-07-12 14:08:26
  ended_at: 2025-07-12 14:08:26
  act_started_at: 2025-07-12 14:08:26
  act_ended_at: 2025-07-12 14:08:26
  organization_id: 1
