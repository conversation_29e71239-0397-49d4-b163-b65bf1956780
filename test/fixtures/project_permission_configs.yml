# == Schema Information
#
# Table name: project_permission_configs
#
#  id                            :bigint           not null, primary key
#  deleted_at(删除时间)          :datetime
#  key(权限key)                  :string(200)
#  name(权限名称)                :string
#  order_number(排序)            :integer
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  organization_id(外键: 组织id) :integer
#  parent_id(父级id)             :integer
#
# Indexes
#
#  index_project_permission_configs_on_organization_id  (organization_id)
#

one:
  order_number: 1
  key: MyString
  name: MyString
  parent_id: 1
  organization_id: 1
  deleted_at: 2025-04-16 23:44:44

two:
  order_number: 1
  key: MyString
  name: MyString
  parent_id: 1
  organization_id: 1
  deleted_at: 2025-04-16 23:44:44
