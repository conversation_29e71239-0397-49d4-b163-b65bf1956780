module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user
    def connect
      @session_key = cookies.encrypted[Rails.application.config.session_options[:key]]
      self.current_user = find_verified_user
    end

    def find_verified_user
      if current_user = User.find_by(id: @session_key["user_id"])
        current_user
      else
        reject_unauthorized_connection
      end
    end


  end
end
