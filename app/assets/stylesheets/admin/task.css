/* 全局样式 - 添加 seven-container 作为作用域容器 */
.seven-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5715;
}

/* 布局容器 */
.seven-flex-container {
  display: flex;
  gap: 8px;
  height: calc(100vh - 55px);
}

/* 左侧详情面板 */
.seven-detail-panel {
  flex: 2.5;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  margin-left: 3px;
}

/* 右侧面板 */
.seven-side-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 卡片样式 */
.seven-card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.06);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.seven-card-header {
  padding: 4px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
}

.seven-card-title {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.seven-card-body {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

/* 信息网格 */
.seven-ticket-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.seven-info-item {
  margin-bottom: 12px;
}

.seven-info-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
  font-weight: 400;
}

.seven-info-value {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  min-height: 22px;
}

/* 状态标签 */
.seven-tag {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 2px;
  border: 1px solid transparent;
}

.seven-tag-blue {
  background: #e6f4ff;
  border-color: #91caff;
  color: #1677ff;
}

.seven-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.seven-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.seven-tag-yellow {
  background: #fffbe6;
  border-color: #ffe58f;
  color: #faad14;
}

/* 描述框 */
.seven-description-box {
  background: #fafafa;
  padding: 16px;
  border-radius: 2px;
  border-left: 3px solid #1677ff;
  margin-top: 8px;
  line-height: 1.7;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

/* 附件样式 */
.seven-file-attachment {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #fafafa;
  border-radius: 2px;
  border: 1px solid #f0f0f0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
  transition: all 0.3s;
}

.seven-file-attachment:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

/* 时间线样式 */
.seven-timeline {
  position: relative;
  padding: 8px 0;
}

.seven-timeline::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: #f0f0f0;
}

.seven-timeline-item {
  position: relative;
  margin-bottom: 20px;
  padding-left: 40px;
}

.seven-timeline-dot {
  position: absolute;
  left: 10px;
  top: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #1677ff;
  z-index: 1;
}

.seven-timeline-date {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
  font-weight: 400;
}

.seven-timeline-content {
  background: #fafafa;
  padding: 12px;
  border-radius: 2px;
  border: 1px solid #f0f0f0;
}

/* 评论样式 */
.seven-comment {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.seven-comment:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.seven-comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1677ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  flex-shrink: 0;
}

.seven-comment-content {
  flex: 1;
}

.seven-comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.seven-comment-user {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.seven-comment-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.seven-comment-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.7;
}

/* 评论表单 */
.seven-comment-form {
  margin-top: 16px;
}

.seven-comment-form textarea {
  width: 97%;
  height: 100px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  resize: vertical;
  margin-bottom: 8px;
  font-size: 14px;
  transition: all 0.3s;
}

.seven-comment-form textarea:focus {
  outline: none;
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.seven-comment-form button {
  padding: 0px 16px;
  background-color: #1677ff;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
  float: right;
}

.seven-comment-form button:hover {
  background-color: #4096ff;
}

/* 按钮样式 */
.seven-action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.seven-btn {
  padding: 4px 12px;
  border-radius: 2px;
  font-weight: 400;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s;
  border: 1px solid transparent;
  font-size: 14px;
  height: 28px;
}

.seven-btn-primary {
  background-color: #1677ff;
  color: white;
  box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
}

.seven-btn-primary:hover {
  background-color: #4096ff;
}

.seven-btn-outline {
  background: transparent;
  border: 1px solid #1677ff;
  color: #1677ff;
}

.seven-btn-outline:hover {
  color: #4096ff;
  border-color: #4096ff;
}

.seven-btn-danger {
  background-color: #ff4d4f;
  color: white;
  box-shadow: 0 2px 0 rgba(255, 77, 79, 0.1);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .seven-flex-container {
    flex-direction: column;
    height: auto;
  }

  .seven-ticket-info-grid {
    grid-template-columns: 1fr;
  }
}
/* 评论布局优化 */
.seven-comment {
  position: relative;
  padding: 12px 0;
}

.seven-comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.seven-comment-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

.seven-comment-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.7;
  padding-right: 40px;
}

/* 回复样式 */
.seven-comment-reply {
  margin-top: 12px;
  padding: 12px;
  background-color: #f9f9f9;
  border-left: 2px solid #1677ff;
  border-radius: 0 4px 4px 0;
  position: relative;
  padding-right: 0px;
}

/* .seven-reply-btn {
  padding: 2px 8px;
  font-size: 12px;
  background: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  position: absolute;
  top: 12px;
  right: 0;
}

.seven-reply-btn:hover {
  background: #e6e6e6;
} */

/* 固定评论框 */
.seven-card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#seven-comments-container {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 16px;
}

.seven-comment-form {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 16px;
}

.seven-padding2{
  padding: 0px 3px;
}