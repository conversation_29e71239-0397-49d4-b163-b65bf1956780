/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

.fr-clearfix::after {
  clear: both;
  display: block;
  content: "";
  height: 0; }

.fr-hide-by-clipping {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

#fr-form-container {
  margin: auto;
  position: relative;
  top: 10%;
  background-color: #f5f5f5;
  width: 60%;
  border-radius: 5px;
  z-index: 9002;
  overflow: auto;
  background-color: #fefefe; }

.fr-trim-video-name {
  font-family: Arial, Helvetica, sans-serif;
  padding-top: 15px; }

.fr-file-loader {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid #53777a;
  display: inline-block !important;
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
  animation: spin 2s linear infinite;
  width: 20px;
  height: 20px;
  display: block !important;
  align-items: center; }

.fr-trim-button {
  margin-top: 5px;
  height: 36px;
  line-height: 1;
  color: #0098f7;
  padding: 10px;
  cursor: pointer;
  text-decoration: none;
  border: none;
  background: none;
  font-size: 16px;
  border-radius: 5px;
  background-color: #eff5fa;
  outline: none; }
  .fr-trim-button:hover {
    background: #ebebeb; }

/* Safari */
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }
@keyframes spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }
.fr-slidecontainer {
  width: 100%; }

.fr-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 15px;
  background: #d3d3d3; }

.fr-video-trim-buttons {
  text-align: right;
  padding-bottom: 5px;
  padding-right: 15px;
  margin-top: 10px; }

.fr-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  cursor: pointer; }

.fr-slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  background: #3498db;
  border-radius: 50%;
  cursor: pointer; }

.fr-range-value-start {
  position: absolute; }

.fr-range-value-start > span {
  width: 60px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: #03a9f4;
  color: #fff;
  font-size: 12px;
  display: block;
  position: absolute;
  left: 50%;
  transform: translate(-85%, 0);
  border-radius: 6px; }

.fr-range-value-start > span:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  left: 86%;
  margin-left: -5px;
  margin-top: -1px; }

.fr-range-value-end {
  position: absolute; }

.fr-range-value-end > span {
  width: 60px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: #03a9f4;
  color: #fff;
  font-size: 12px;
  display: block;
  position: absolute;
  left: 14%;
  transform: translate(-8%, 0);
  border-radius: 6px; }

.fr-range-value-end > span:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  left: 14%;
  margin-left: -5px;
  margin-top: -1px; }

#startTimeValue {
  top: -153% !important; }

#startTimeValue span:before {
  border-top: 10px solid #03a9f4;
  top: 100%; }

#endTimeValue {
  top: -153% !important; }

#endTimeValue span:before {
  border-top: 10px solid #03a9f4;
  top: 100%; }

.fr-range-slider {
  position: relative;
  width: 80%;
  height: 15px; }

.fr-range-slider > input {
  pointer-events: none;
  position: absolute;
  left: 0;
  top: 10px;
  width: 100%;
  outline: none;
  height: 6px;
  border-radius: 10px; }

.fr-range-slider > input::-webkit-slider-thumb {
  pointer-events: all;
  position: relative;
  z-index: 1;
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  background: #3498db;
  cursor: pointer;
  border-radius: 7px;
  margin-top: -3.6px; }

.fr-range-slider > input::-moz-range-thumb {
  pointer-events: all;
  position: relative;
  z-index: 10;
  -moz-appearance: none;
  width: 9px;
  height: 15px;
  border-radius: 7px;
  margin-top: -3.6px; }

.fr-range-slider > input::-moz-range-track {
  position: relative;
  z-index: -1;
  background-color: black;
  border: 0;
  height: 15px;
  border-radius: 50%;
  margin-top: -3.6px; }

.fr-range-slider > input:last-of-type::-moz-range-track {
  -moz-appearance: none;
  background: none transparent;
  border: 0; }

.fr-range-slider > input[type="range"]::-moz-focus-outer {
  border: 0; }

@media screen and (max-width: 430px) {
  .range-value span {
    width: 40px;
    font-size: 10px; }

  #fr-form-container {
    top: 20%; } }
#fr-video-edit {
  width: 80%;
  text-align: center;
  height: 50%;
  margin-bottom: 20px;
  padding-top: 8px;
  padding-bottom: 8px; }
