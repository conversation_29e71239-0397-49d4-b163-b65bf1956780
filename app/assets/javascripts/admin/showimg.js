// layui 自带的图片弹出
$(document).on('click', 'img', function(){
  var src = $(this).attr('src')
  var alt = $(this).attr('alt')
  var tag = $(this).attr('data-tag')
  if (tag !== undefined ){return false;}

  layer.photos({
    photos: {
      "title": "",
      "start": 0,
      "data": [
        {
          "alt": alt,
          "pid": 99999999999999,
          "src": src,
        }
      ]
    },
    footer: false
  });
  })