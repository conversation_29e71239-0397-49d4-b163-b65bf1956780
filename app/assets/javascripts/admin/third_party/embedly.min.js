/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(E){"use strict";E=E&&E.hasOwnProperty("default")?E["default"]:E,Object.assign(E.POPUP_TEMPLATES,{"embedly.insert":"[_BUTTONS_][_URL_LAYER_]","embedly.edit":"[_BUTTONS_]"}),Object.assign(E.DEFAULTS,{embedlyKey:null,embedlyInsertButtons:["embedlyBack","|"],embedlyEditButtons:["embedlyRemove"],embedlyScriptPath:"https://cdn.embedly.com/widgets/platform.js"}),E.PLUGINS.embedly=function(a){var d,i,o=a.$;function t(e){a.events.on("html.processGet",s),e&&a.html._setHtml(a.$el,a.html.get()),a.events.$on(a.$el,"click touchend","div.fr-embedly",l),a.events.on("mousedown window.mousedown",m),a.events.on("window.touchmove",u),a.events.on("mouseup window.mouseup",b),a.events.on("commands.mousedown",function(e){0<e.parents(".fr-toolbar").length&&b()}),a.events.on("blur video.hideResizer commands.undo commands.redo element.dropped",function(){b(!0)}),a.events.on("element.beforeDrop",function(e){if(e.hasClass("fr-embedly"))return e.html(e.attr("data-original-embed")),e}),a.events.on("keydown",function(e){var t=e.which;return!d||t!=E.KEYCODE.BACKSPACE&&t!=E.KEYCODE.DELETE?d&&t==E.KEYCODE.ESC?(b(!0),e.preventDefault(),!1):d&&t!=E.KEYCODE.F10&&!a.keys.isBrowserAction(e)?(e.preventDefault(),!1):void 0:(e.preventDefault(),c(),!1)},!0),a.events.on("toolbar.esc",function(){if(d)return a.events.disableBlur(),a.events.focus(),!1},!0),a.events.on("toolbar.focusEditor",function(){if(d)return!1},!0),a.events.on("snapshot.after",function(e){var t=a.doc.createElement("div");t.innerHTML=e.html,s(t),e.html=t.innerHTML}),a.win.embedly&&(a.win.embedly("on","card.rendered",function(e){n(e)}),a.win.embedly("on","card.resize",function(e){n(e)})),f(!0)}function n(e){var t=o(e);t.parents(".fr-embedly").attr("contenteditable",!1).attr("draggable",!0).addClass("fr-draggable").css("height",t.height()).addClass("fr-draggable"),a.opts.iframe&&a.size.syncIframe()}function l(e){d=o(this),function s(){i||function n(){a.shared.$embedly_resizer?(i=a.shared.$embedly_resizer,a.shared.$embedly_overlay,a.events.on("destroy",function(){o("body").first().append(i)},!0)):(a.shared.$embedly_resizer=o(document.createElement("div")).attr("class","fr-embedly-resizer"),i=a.shared.$embedly_resizer,a.events.$on(i,"mousedown",function(e){e.stopPropagation()},!0));a.events.on("shared.destroy",function(){i.html("").removeData().remove(),i=null},!0)}();(a.$wp||a.$sc).append(i),i.data("instance",a);var e=0,t=0;a.opts.iframe&&(t=a.helpers.getPX(a.$wp.find(".fr-iframe").css("padding-top")),e=a.helpers.getPX(a.$wp.find(".fr-iframe").css("padding-left")));i.css("top",(a.opts.iframe?d.offset().top+t-1+a.$iframe.position().top:d.offset().top-a.$wp.offset().top-1)+a.$wp.scrollTop()).css("left",(a.opts.iframe?d.offset().left+e-1:d.offset().left-a.$wp.offset().left-1)+a.$wp.scrollLeft()).css("width",d.outerWidth()).css("height",d.height()).addClass("fr-active")}(),function r(){var e=a.popups.get("embedly.edit");e||(e=function s(){var e="";if(0<a.opts.embedlyEditButtons.length){var t={buttons:e+='<div class="fr-buttons">\n      '.concat(a.button.buildList(a.opts.embedlyEditButtons),"\n      </div>")},n=a.popups.create("embedly.edit",t);return a.events.$on(a.$wp,"scroll.emebdly-edit",function(){d&&a.popups.isVisible("embedly.edit")&&(a.events.disableBlur(),function t(e){l.call(e.get(0))}(d))}),n}return!1}());if(e){a.popups.setContainer("embedly.edit",a.$sc),a.popups.refresh("embedly.edit");var t=d.offset().left+d.outerWidth()/2,n=d.offset().top+d.outerHeight();a.popups.show("embedly.edit",t,n,d.outerHeight())}}()}function s(e){if(e&&a.node.hasClass(e,"fr-embedly"))e.innerHTML=e.getAttribute("data-original-embed"),e.removeAttribute("draggable"),e.removeAttribute("contenteditable"),e.setAttribute("class",(e.getAttribute("class")||"").replace("fr-draggable",""));else if(e&&e.nodeType==Node.ELEMENT_NODE)for(var t=e.querySelectorAll(".fr-embedly"),n=0;n<t.length;n++)s(t[n])}function f(e){if(e)return a.popups.onRefresh("embedly.insert",r),!0;var t="";0<a.opts.embedlyInsertButtons.length&&(t+='<div class="fr-buttons fr-tabs">',t+=a.button.buildList(a.opts.embedlyInsertButtons),t+="</div>");var n={buttons:t,url_layer:'<div class="fr-embedly-layer fr-active fr-layer" id="fr-embedly-layer-'+a.id+'"><div class="fr-input-line"><input id="fr-embedly-layer-text-'+a.id+'" type="text" placeholder="'+a.language.translate("Paste in a URL to embed")+'" tabIndex="1" aria-required="true"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="embedlyInsert" tabIndex="2" role="button">'+a.language.translate("Insert")+"</button></div></div>"};return a.popups.create("embedly.insert",n)}function r(){a.popups.get("embedly.insert").find(".fr-embedly-layer input").val("").trigger("change")}function e(e){if(e.length){var t="<a href='"+e+"' data-card-branding='0' class='embedly-card'"+(a.opts.embedlyKey?" data-card-key='"+a.opts.embedlyKey+"'":"")+"></a>";if(a.opts.trackChangesEnabled){a.edit.on(),a.events.focus(!0),a.selection.restore(),a.undo.saveStep(),a.markers.insert(),a.html.wrap();var n=a.$el.find(".fr-marker");a.node.isLastSibling(n)&&n.parent().hasClass("fr-deletable")&&n.insertAfter(n.parent()),n.replaceWith('<div class="fr-embedly fr-draggable" draggable="true" contenteditable="false" data-original-embed="'+t+'">'+t+"</div>"),a.selection.clear()}else a.html.insert('<div class="fr-embedly fr-draggable" draggable="true" contenteditable="false" data-original-embed="'+t+'">'+t+"</div>");a.popups.hideAll()}}function c(){if(d&&!1!==a.events.trigger("embedly.beforeRemove",[d])){var e=d;if(a.popups.hideAll(),b(!0),a.opts.trackChangesEnabled){var t=e.find(".embedly-card"),n="pending-".concat(a.id,"-").concat(a.track_changes.getPendingChanges().length),s=a.opts.showChangesEnabled?"fr-highlight-change":"";return e.data("track-id",n),e.addClass(s),e.data("tracking","true"),e.data("tracking-deleted","true"),e.attr("contenteditable","false"),t.addClass("fr-tracking-deleted"),t.css("height","100%"),void a.track_changes.pushChange(n)}a.selection.setBefore(e.get(0))||a.selection.setAfter(e.get(0)),e.remove(),a.selection.restore(),a.html.fillEmptyBlocks(),a.undo.saveStep(),a.events.trigger("video.removed",[e])}}function b(e){d&&(function t(){return a.shared.embedly_exit_flag}()||!0===e)&&(i.removeClass("fr-active"),a.toolbar.enable(),d.removeClass("fr-active"),d=null,u())}function m(){a.shared.embedly_exit_flag=!0}function u(){a.shared.embedly_exit_flag=!1}return a.shared.embedly_exit_flag=!1,{_init:function p(){if(!a.$wp)return!1;if("undefined"!=typeof embedly)t(!0);else if(a.shared.embedlyLoaded)a.shared.embedlyCallbacks.push(t);else{a.shared.embedlyLoaded=!0,a.shared.embedlyCallbacks=[],a.shared.embedlyCallbacks.push(t);var e=a.doc.createElement("script");e.type="text/javascript",e.src=a.opts.embedlyScriptPath,e.innerText="",e.onload=function(){if(a.shared.embedlyCallbacks)for(var e=0;e<a.shared.embedlyCallbacks.length;e++)a.shared.embedlyCallbacks[e]()},a.doc.getElementsByTagName("head")[0].appendChild(e)}},showInsertPopup:function h(){var e=a.$tb.find('.fr-command[data-cmd="embedly"]'),t=a.popups.get("embedly.insert");if(t||(t=f()),!t.hasClass("fr-active"))if(a.popups.refresh("embedly.insert"),a.popups.setContainer("embedly.insert",a.$tb),e.isVisible()){var n=a.button.getPosition(e),s=n.left,r=n.top;a.popups.show("embedly.insert",s,r,e.outerHeight())}else a.position.forSelection(t),a.popups.show("embedly.insert")},insert:function y(){e(a.popups.get("embedly.insert").find(".fr-embedly-layer input").val())},remove:c,get:function g(){return d},add:e,back:function v(){d?(a.events.disableBlur(),d.trigger("click")):(a.events.disableBlur(),a.selection.restore(),a.events.enableBlur(),a.popups.hide("embedly.insert"),a.toolbar.showInline())}}},E.DefineIcon("embedly",{NAME:"share-alt",SVG_KEY:"insertEmbed"}),E.RegisterCommand("embedly",{undo:!0,focus:!0,title:"Embed URL",popup:!0,callback:function(){this.popups.isVisible("embedly.insert")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("embedly.insert")):this.embedly.showInsertPopup()},plugin:"embedly"}),E.RegisterCommand("embedlyInsert",{undo:!0,focus:!0,callback:function(){this.embedly.insert()}}),E.DefineIcon("embedlyRemove",{NAME:"trash",SVG_KEY:"remove"}),E.RegisterCommand("embedlyRemove",{title:"Remove",undo:!1,callback:function(){this.embedly.remove()}}),E.DefineIcon("embedlyBack",{NAME:"arrow-left",SVG_KEY:"back"}),E.RegisterCommand("embedlyBack",{title:"Back",undo:!1,focus:!1,back:!0,callback:function(){this.embedly.back()},refresh:function(e){this.embedly.get()||this.opts.toolbarInline?(e.removeClass("fr-hidden"),e.next(".fr-separator").removeClass("fr-hidden")):(e.addClass("fr-hidden"),e.next(".fr-separator").addClass("fr-hidden"))}})});