// 自定义富文本
function BuildFroalaEditor (name){
  var editorInstance = new FroalaEditor(name, {
    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', '|',  'fontFamily', 'fontSize', 'textColor', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '|', 'insertLink', 'insertImage', 'insertVideo', 'insertFile', 'insertTable', '|', 'print', 'help', 'fullscreen', '|', 'undo', 'redo'],
    // toolbarButtons: ['undo', 'redo', 'clearFormatting', '|', 'bold', 'italic', 'underline','strikeThrough','|', 'fontFamily', 'fontSize', 'color', '|','paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', 'quote', '-', 'insertLink', 'insertImage', 'insertVideo', 'embedly', 'insertFile', 'insertTable', '|', 'emoticons', 'specialCharacters', 'insertHR', 'selectAll', '|', 'print', 'spellChecker', 'help', '|', 'fullscreen'],
    // quickInsertButtons: ['link', 'image', 'video', 'table', 'myButton'],
    // pluginsEnabled: ['link', 'image', 'video',  'table', 'lists'],
    imageDefaultAlign: 'left', // 插入图片默认居左显示
    options: '',
    toolbarSticky: false, // 是否自动吸顶
    fileUploadURL: '/upload_file',
    imageUploadURL: '/upload_image',
    videoUploadURL: '/upload_video',
    language: "zh_cn"
  })
  return editorInstance
}

function CommentEditor(name) {
  var editorInstance = new FroalaEditor(name, {
    height: 50, // 设置编辑器的高度为300px
    width: '100%', // 设置编辑器的宽度为父容器的100%
    toolbarButtons: ['bold', 'paragraphFormat', 'align', 'insertLink', 'insertImage', 'insertVideo', 'insertFile', 'insertTable', '|', 'undo', 'redo'],
    fileUploadURL: '/upload_file',
    imageUploadURL: '/upload_image',
    videoUploadURL: '/upload_video',
    language: "zh_cn",
    toolbarSticky: false, // 是否自动吸顶
    charCounterCount: false
  });
  return editorInstance;
}