/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(U){"use strict";function j(e){return function t(e){if(Array.isArray(e))return i(e)}(e)||function a(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function n(e,t){if(!e)return;if("string"==typeof e)return i(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);"Object"===a&&e.constructor&&(a=e.constructor.name);if("Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return i(e,t)}(e)||function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}U=U&&U.hasOwnProperty("default")?U["default"]:U,Object.assign(U.DEFAULTS,{trackChangesEnabled:!1,showChangesEnabled:!1}),U.PLUGINS.track_changes=function(M){var _=M.$;_.fn.isAfter=function(e){var t=_(this),a=t.parents(),n=e.parents(),r=t;for(var i in a){var s=e;if(a.hasOwnProperty(i)){var l=_(a[i]);for(var o in n)if(n.hasOwnProperty(o)){var c=_(n[o]);if(l[0]===c[0]){var d=r.index();return s.index()<d}s=c}r=l}}return!1};var l,i,s=[],I="",w="",L="",T="",d=[],o=[],g=null,P=[],h=!1;function c(){return s.pop()}function f(e){var t=!(!e||8!=e.keyCode),a=e&&13==e.keyCode,n=!(!e||46!=e.keyCode);if(M.opts.trackChangesEnabled){M.selection.restore(),e&&e.composing&&(M.selection.save(),M.selection.ranges()[0].collapse(!1));var r=M.selection.ranges(0).startContainer,i=_(r).parentsUntil(M.$el,"[data-tracking=true]");if(_(r).data("tracking")&&t){M.markers.insert();var s=M.$el.find(".fr-marker");return s[0].previousSibling&&"IMG"===s[0].previousSibling.tagName||M.selection.clear(),void _(s).remove()}if(!i.length||"TD"==r.tagName||a){if(M.selection.isCollapsed()){M.markers.insert();var l=M.$el.find(".fr-marker");if(e&&e.composing&&(l=_(l[1])),!l.length)return;var o="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),c=M.opts.showChangesEnabled?'class="fr-highlight-change"':"";if(t||n){if(t){if(!l[0].previousSibling||l[0].previousSibling.nodeType!==Node.ELEMENT_NODE||!l[0].previousSibling.lastChild||l[0].previousSibling.lastChild.nodeType!==Node.ELEMENT_NODE||"IMG"!==l[0].previousSibling.lastChild.tagName){for(;_(l[0].previousSibling).data("tracking");)l.insertBefore(l.prev());_(l[0].previousSibling).find("[data-tracking=true]").length&&l.insertBefore(_(l[0].previousSibling).find("[data-tracking=true]").eq(0))}}else if(_(r).data("tracking")){if(!r.nextSibling)return void(l[0].nextSibling&&"IMG"===l[0].nextSibling.tagName&&l[0].nextSibling.remove());l.insertBefore(r.nextSibling),R(l)}else R(l);var d=l[0]&&l[0].previousSibling?l[0].previousSibling.nodeValue:"",g=d&&d.slice(0,-(e.selectionLength?e.selectionLength:1));n&&(l[0].nextSibling&&"SPAN"!==l[0].nextSibling.tagName&&null===l[0].nextSibling.nodeValue&&l[0].nextSibling.firstChild&&l[0].nextSibling.firstChild.before(l[0]),g=(d=l[0].nextSibling?l[0].nextSibling.nodeValue:" ")&&d.slice(1)),n&&l[0].nextSibling&&"isPasted"===l[0].nextSibling.id?g=(d=l[0].nextSibling?l[0].nextSibling.textContent:" ")&&d.slice(1):t&&l[0].previousSibling&&"isPasted"===l[0].previousSibling.id&&(g=(d=l[0].previousSibling?l[0].previousSibling.textContent:" ")&&d.slice(0,-(e.selectionLength?e.selectionLength:1)));var h,f=_('<span data-tracking="true" data-track-id="'.concat(o,'" ').concat(c,"></span>")),p=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" '.concat(M.helpers.isIOS()?"":'contenteditable="false"',"></span>"));M.opts.showChangesEnabled||(M.helpers.isMobile()?p.addClass("fr-track-hide-mobile"):p.addClass("fr-track-hide"));var u=!1,k=!1;if(t)try{var m=l[0]&&l[0].previousSibling;if(l[0]&&l[0].previousElementSibling&&"I"===l[0].previousElementSibling.tagName&&!d&&(h=l[0].previousElementSibling),m&&3===m.nodeType&&"SPAN"!==m.parentNode.tagName&&m.previousSibling&&"SPAN"===m.previousSibling.tagName&&" "===m.nodeValue?h=m.previousSibling:m&&"SPAN"===m.parentNode.tagName&&0<_(m.parentNode).find(".fr-emoticon").length?(h=m.parentNode,u=!0):m&&("SPAN"===m.tagName&&"isPasted"!==m.id||"HR"===m.tagName)?h=m:_(l).parent().prev().is("hr")?(h=_(l).parent().prev().get(0),u=k=!0):m&&"IMG"===m.tagName&&(h=m,_(f).prepend("".concat(U.MARKERS)),_(f).append("".concat(U.MARKERS))),M.helpers.isMobile()&&l[0]&&!h&&!d&&!g)return;u?(_(l[0].parentNode).after(f),k?_(l).parent().remove():_(l).remove()):l[0]?e.composing?_(l).after(f):_(l).before(f):_(T).before(f)}catch(e){T&&_(T).before(f)}else if(n){var b=l[0].nextSibling;l[0].nextElementSibling&&"I"===l[0].nextElementSibling.tagName&&!l[0].nextSibling.nodeValue?h=l[0].nextElementSibling:b&&3===b.nodeType&&"SPAN"!==b.parentNode.tagName&&b.nextSibling&&"SPAN"===b.nextSibling.tagName&&" "===b.nodeValue?h=b.nextSibling:b&&"SPAN"===b.parentNode.tagName&&0<_(b.parentNode).find(".fr-emoticon").length?(h=b.parentNode,u=!0):b&&"SPAN"===b.tagName&&l[0].nextSibling&&"isPasted"!==l[0].nextSibling.id?h=b:b&&"IMG"===b.tagName&&(h=b,_(f).prepend("".concat(U.MARKERS)),_(f).append("".concat(U.MARKERS))),u?(_(l[0].parentNode).before(f),_(l).remove()):_(l).after(f)}if(e.composing?P.push({span:f,"delete":p}):(M.helpers.isIOS()&&P.push({"delete":p}),_(f).prepend(p)),!h&&(h=d&&d.replace(g,""),M.helpers.isMobile()&&I&&!e.composing)){var v=M.selection.get().focusNode,C=M.selection.get().focusOffset,S=1;w&&v&&w.isSameNode(v)&&(S=L-C),h=I.replace(/\u00a0/g," ").replace(d.replace(/\u00a0/g," "),"").slice(0,S)}if(h&&(u?(_(p).append(_(_(h).get(0).outerHTML)),M.markers.insert()):_(p).append(h)),h&&0==h.length)return;M.browser.msie&&t&&(_(f).prepend("".concat(U.MARKERS)),_(f).append("".concat(U.MARKERS))),t&&(g||""===g)&&l[0].previousSibling?l[0].previousSibling.nodeValue=g:n&&(_(f).append("".concat(U.MARKERS)),l[0].nextSibling.nodeValue=g)}else{var N=U.INVISIBLE_SPACE;if(e&&M.helpers.isMobile()&&((N=l[0]&&l[0].previousSibling&&l[0].previousSibling.nodeValue?l[0].previousSibling.nodeValue.slice(-1):U.INVISIBLE_SPACE)&&l[0]&&l[0].previousSibling&&l[0].previousSibling.nodeValue?l[0].previousSibling.nodeValue=l[0].previousSibling.nodeValue.slice(0,-1):!l[0]||l[0].previousSibling||l[0].nextSibling||N!==U.INVISIBLE_SPACE||(N=M.selection.get().focusNode.nodeValue.slice(L,M.selection.get().focusOffset),M.selection.get().focusNode.nodeValue=M.selection.get().focusNode.nodeValue.slice(M.selection.get().focusOffset))),a){if(l.parent().parent().is("li"))return;var E=_("<p></p>");return void(0<l.closest("p").length&&(l.closest("p").after(E),E.append(l),M.markers.remove(),setTimeout(function(){E.prev().text()!==String.fromCharCode(8203)&&""!==E.prev().text()||E.prev().remove(),M.selection.setAfter(E[0])},1)))}l[0].previousSibling&&"BR"===l[0].previousSibling.tagName&&l[0].previousSibling.remove(),l.replaceWith('<span data-tracking="true" data-track-id="'.concat(o,'" ').concat(c,">").concat(N+U.MARKERS,"</span>"))}M.selection.restore(),M.track_changes.pushChange(o)}else{M.selection.save();var y=M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),x=M.$el.find('.fr-marker[data-type="false"]').length&&M.$el.find('.fr-marker[data-type="false"]').get(0);if(_(y).parent().hasClass("fr-emoticon")&&_(y).parent().before(y),_(y).isAfter(_(x))){var A=y;y=x,x=A}$(y,x,e&&e.deletion),M.selection.restore()}M.helpers.isMobile()&&I&&M.markers.remove()}}}function R(e){if(_(e[0].nextSibling).data("tracking")){for(;_(e[0].nextSibling).data("tracking")&&(!(0<_(e[0].previousElementSibling).find("[data-tracking-deleted=true]").length&&0<_(e[0].nextElementSibling).find("[data-tracking-deleted=true]").length)||_(e[0].previousSibling).data("tracking"));)_(e).insertAfter(e.next());e.insertBefore(e[0].previousSibling)}}function $(e,t){var a=2<arguments.length&&arguments[2]!==undefined&&arguments[2];if(e&&(!e.isSameNode(t)||!_(e).hasClass("fr-marker"))){for(;e.nodeType===Node.COMMENT_NODE;)e=e.nextSibling;if(_(e).parent().is("table")&&0!==_(e).parent().next().text().length&&$(_(e).parent().next()[0],t,a),"TD"!==e.tagName||1!==e.childNodes.length||"BR"!==e.childNodes[0].tagName){if(M.node.isBlock(e)&&"HR"!==e.tagName)return M.node.hasClass(e.firstChild,"fr-marker")?$(e.firstChild.nextSibling,t,a):$(e.firstChild,t,a),!1;if("BR"===e.tagName&&"TD"===e.parentElement.tagName){var n=e.parentElement.nextElementSibling;_(e).remove(),$(n,t)}var r="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),i=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",s=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),l=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" '.concat(M.helpers.isIOS()?"":'contenteditable="false"',"></span>"));M.opts.showChangesEnabled||(M.helpers.isMobile()?l.addClass("fr-track-hide-mobile"):l.addClass("fr-track-hide"));var o=e;for(_(e).before(s),M.track_changes.pushChange(r);o&&_(o).hasClass("fr-marker");)o=o.nextSibling;for(var c=!1;o&&!c&&!_(o).hasClass("fr-marker")&&!_(o).is("[data-track-id]")&&0===_(o).find("fr-marker").length&&"UL"!==o.tagName&&"OL"!==o.tagName;){var d=o;if("IMG"===o.tagName&&_(o).data("tracking-img","true"),M.node.isBlock(o)&&"HR"!==e.tagName)return $(o.firstChild,t,a),!1;if(o)for(var g=o.childNodes,h=0;h<g.length;h++)if(g[h].className&&"fr-marker"===g[h].className){c=!0,$(o.firstChild,t,a);break}c||(o=o.nextSibling,_(l).append(d))}if(!o||"UL"!==o.tagName&&"OL"!==o.tagName||$(o,t,a),a?P.push({span:s,"delete":l}):(M.helpers.isIOS()&&P.push({"delete":l}),_(s).prepend(l)),M.browser.msie?_(s).prepend("".concat(U.INVISIBLE_SPACE+U.MARKERS)):_(s).prepend("".concat(U.INVISIBLE_SPACE)),_(s).append("".concat(U.INVISIBLE_SPACE+U.MARKERS)),!o&&!e.isSameNode(t)){for(var f=s.get(0).parentNode;f&&!f.nextSibling&&!M.node.isElement(f);)f=f.parentNode;if(f){var p=f.nextSibling;p&&(M.node.isBlock(p)?"HR"===p.tagName?$(p.nextSibling,t,a):(M.browser.mozilla&&"LI"===f.tagName&&"SPAN"===f.lastChild.tagName&&_(f.lastChild).hasClass("fr-marker")&&f.lastChild.remove(),$(p.firstChild,t,a)):$(p,t,a))}}}}}function p(e){if(M.opts.trackChangesEnabled){M.selection.restore();var t=M.selection.ranges(0).startContainer,a=_(t).parentsUntil(M.$el,"[data-tracking=true]");if(_(t).data("tracking"))return;if(!a.length&&!M.selection.isCollapsed()&&"quote"!=e){M.selection.save(),u(M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),M.$el.find('.fr-marker[data-type="false"]').length&&M.$el.find('.fr-marker[data-type="false"]').get(0));var n=M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),r=n.parentNode;if(r&&"A"===r.tagName&&r.firstChild&&"SPAN"===r.firstChild.tagName&&2<r.firstChild.childNodes.length&&"STRONG"===r.firstChild.childNodes[3].tagName)_(r.firstChild.childNodes[3].firstChild).before(n);else{for(var i=n.previousSibling.firstChild;i&&"SPAN"!==i.tagName;)i=i.nextSibling;_(i).after(n)}M.selection.restore()}}}function u(e,t,a){if(e&&!("<br>"===_(e).html()&&_(e).parent().is("td")||(_(e).parent().is("table")&&0!==_(e).parent().next().text().length&&u(_(e).parent().next()[0],t,a),_(e).is("br")&&_(e).parent().length&&_(e).parent().is("td")&&0===_(e).parent().text().length))){for(a||(a="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),M.track_changes.pushChange(a));e.nodeType===Node.COMMENT_NODE;)e=e.nextSibling;if(M.node.isBlock(e)&&"HR"!==e.tagName)return M.node.hasClass(e.firstChild,"fr-marker")?u(e.firstChild.nextSibling,t,a):u(e.firstChild,t,a),!1;var n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=_('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>")),i=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false" style="display:none"></span>'),s=e;for(_(e).before(r);s&&_(s).hasClass("fr-marker");)s=s.nextSibling;for(var l=!1;s&&!l&&!_(s).hasClass("fr-marker")&&0===_(s).find("fr-marker").length&&"UL"!==s.tagName&&"OL"!==s.tagName;){var o=s;if(M.node.isBlock(s)&&"HR"!==e.tagName)return u(s.firstChild,t,a),!1;if(s)for(var c=s.childNodes,d=0;d<c.length;d++)if(c[d].className&&"fr-marker"===c[d].className){l=!0,u(s.firstChild,t,a);break}l||(s=s.nextSibling,_(i).append(o.cloneNode(!0)),_(r).append(o))}if(!s||"UL"!==s.tagName&&"OL"!==s.tagName||u(s,t,a),_(s).hasClass("fr-marker")&&_(r).append(s),_(r).prepend(i),_(r).prepend("".concat(U.INVISIBLE_SPACE)),_(r).append("".concat(U.INVISIBLE_SPACE)),!s&&!e.isSameNode(t)){for(var g=r.get(0).parentNode;g&&!g.nextSibling&&!M.node.isElement(g);)g=g.parentNode;if(g){var h=g.nextSibling;h&&(M.node.isBlock(h)?"HR"===h.tagName?u(h.nextSibling,t,a):u(h.firstChild,t,a):u(h,t,a))}}}}function k(e,t,a){var n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=_('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>")),i=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');M.opts.showChangesEnabled||i.addClass("fr-track-hide"),i.append(t.get(0).innerHTML),r.append(i);var s=e.get(0).innerHTML;e.html(""),e.append(r),r.append(s)}function m(){if(M.events.trigger("image.hideResizer"),M.events.trigger("video.hideResizer"),M.html.cleanEmptyTags(!0),M.helpers.isMobile())for(var e=M.$el.find("img[data-tracking-img]"),t=0;t<e.length;t++){var a=_(e.get(t));a.parent()&&a.parent().data("tracking")||a.removeData("tracking-img")}}function e(){M.$el.find("li").each(function(){0===this.innerText.replace(/\u200B/g,"").length&&this.remove()})}function t(){var e=c();if(e){var t,a=M.$el.find("[data-track-id=".concat(e,"]"));if(a.length){var n=a.parent("a");if(n.length){var r=_(n.get(0).outerHTML).text();1===[].filter.call(r,function(e){return 8203!==e.charCodeAt(0)}).length&&(t=n)}for(var i=0;i<a.length;i++){var s=a.get(i),l=_(a).data("tracking-deleted")?_(a):_(s).find("[data-tracking-deleted=true]");l.length&&(l.get(0).remove?l.get(0).remove():l.get(0).parentNode.removeChild(l.get(0))),s.hasAttribute("style")?(_(s).removeData("tracking"),_(s).removeData("track-id"),_(s).removeClass("fr-highlight-change")):(M.selection.save(),0<_(s).find("IFRAME").length?(_(s).removeData("tracking"),_(s).removeData("track-id"),_(s).removeClass("fr-highlight-change")):s.outerHTML=s.innerHTML,M.selection.restore())}}t&&t.remove()}m()}function a(){var e=c();if(e){var t=M.$el.find("[data-track-id=".concat(e,"]"));if(t.length){M.selection.save();for(var a=t.length-1;0<=a;a--){var n=_(t).data("tracking-deleted")?_(t):_(t.get(a)).find("[data-tracking-deleted=true]");if(n.length)if(0<_(t).find("IFRAME").length)_(t).removeData("tracking"),_(t).removeData("track-id"),_(t).removeClass("fr-highlight-change"),_(t).removeClass("fr-tracking-deleted"),_(t).removeData("tracking-deleted"),_(t).removeAttr("contenteditable");else n.parent("[data-tracking=true]").data("track-id")==e?(_(t.get(a)).find("[data-tracking-deleted=true]").remove(),t.get(a).outerHTML=n.get(0).innerHTML):t.get(a).remove?t.get(a).remove():t.get(a).parentNode.removeChild(t.get(a));else t.get(a).remove?t.get(a).remove():t.get(a).parentNode.removeChild(t.get(a))}M.selection.restore()}}m()}return M.events.on("keydown",function(e){var t,a;if(!M.opts.trackChangesEnabled)return!0;if(l=null===(t=M.selection.get())||void 0===t?void 0:null===(a=t.focusNode)||void 0===a?void 0:a.nodeValue,M.helpers.isAndroid()){var n,r,i=M.selection.get();I=null==i?void 0:null===(n=i.focusNode)||void 0===n?void 0:n.nodeValue,w=null==i?void 0:i.focusNode,T=null===(r=w)||void 0===r?void 0:r.nextSibling,L=null==i?void 0:i.focusOffset}else if(function s(e){return(!M.keys.ctrlKey(e)||e.which!==U.KEYCODE.Z&&e.which!==U.KEYCODE.C)&&(48<=e.keyCode&&e.keyCode<=57||65<=e.keyCode&&e.keyCode<=90||97<=e.keyCode&&e.keyCode<=122||186<=e.keyCode&&e.keyCode<=222||13===e.keyCode||8===e.keyCode||32===e.keyCode||46===e.keyCode||96<=e.keyCode&&e.keyCode<=111)}(e))if(!M.keys.ctrlKey(e)||"b"!==e.key&&"i"!==e.key&&"u"!==e.key){if(M.keys.ctrlKey(e)&&"a"===e.key||M.keys.ctrlKey(e)&&"s"===e.key)return;f(e)}else p()},!0),M.events.on("keyup",function(e){if(!M.opts.trackChangesEnabled)return!0;var t,a;if(M.helpers.isMobile())if(i=null===(t=M.selection.get())||void 0===t?void 0:null===(a=t.focusNode)||void 0===a?void 0:a.nodeValue,h){for(var n=0;n<P.length;n++)M.opts.showChangesEnabled&&_(P[n].span).empty(),_(P[n].span).prepend(P[n]["delete"]);h=!(P=[])}else if(M.helpers.isIOS()){for(var r=0;r<P.length;r++)i&&1===i.length&&l&&1==l.length?(_(P[r]["delete"]).attr("contenteditable","true"),_(P[r]["delete"]).addClass("fr-ios-mobile-disable-select")):_(P[r]["delete"]).attr("contenteditable","false");P=[]}else{if(46===e.keyCode)return!0;f(e)}},!0),M.events.on("beforeinput",function(e){var t=e.originalEvent,a=!1;if(!M.opts.trackChangesEnabled)return!0;if(M.helpers.isAndroid()){var n=M.selection.get(),r=M.selection.ranges(0),i=r.startContainer,s=r.endContainer,l=_(i).parentsUntil(M.$el,"[data-tracking=true]"),o=_(s).parentsUntil(M.$el,"[data-tracking=true]");if("insertCompositionText"===t.inputType)if(""===t.data)a=!0;else if(n.toString().length>t.data.length){if(0<l.length||0<o.length)return h=!0,e.preventDefault(),!1;a=!0,e.composing=!0}if("insertText"!==t.inputType||M.selection.isCollapsed()||(a=!0),a||"deleteContentBackward"===t.inputType){var c,d;if(h=!0,i.lastChild&&!n.toString()&&null!==(c=i.lastChild.children)&&void 0!==c&&null!==(d=c.item(0))&&void 0!==d&&d.hasAttribute("contenteditable"))return;e.keyCode=8,e.deletion=!0,f(e)}}},!0),M.events.on("paste.before",function(e){M.opts.trackChangesEnabled&&M.helpers.isMobile()&&f(e)},!0),M.events.on("paste.after",function(e){if(M.opts.trackChangesEnabled){M.markers.insert();for(var t=M.$el.find(".fr-marker"),a=t.parent().data("tracking")?t.parent():t.parent().find("[data-tracking=true]"),n=a.children().eq(0).is("ul"),r=t.next();r.is("br");)(r=r.next()).prev().remove(),r.next().is("br")&&r.next().remove();if(r.is("ul")){var i=r.find("li");i.find("[data-tracking=true]").addClass("fr-highlight-change");var s=i[0].innerHTML;if(i.html(""),i.insertAfter(t.closest("li")),0==r.parent().find("[data-tracking=true]").length)return void i.append(s);if(r.remove(),a.data("tracking")){var l=a[0].innerHTML;0<l.trim().length&&a.parent().append(l),i.append(a),a.html(s)}else if(!n)return a.insertBefore(a.closest("ul")),void a.next().remove();M.markers.remove(),i.next().length&&i.next().is("li")&&""==i.next().text().trim()&&i.next().remove();var o=i.prev().children().eq(0);o.is("br")&&o.remove()}}},!0),M.events.on("image.inserted",function(e){M.helpers.isMobile()&&M.opts.trackChangesEnabled&&e.data("tracking-img",!0)}),M.events.on("image.removed",function(e){if(M.helpers.isMobile()&&M.opts.trackChangesEnabled&&!e.data("tracking-img")&&!e.parent().data("tracking")){var t=M.selection.ranges(0).startContainer,a=_(t).parentsUntil(M.$el,"[data-tracking=true]");M.markers.insert();var n=M.$el.find(".fr-marker"),r="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),i=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",s=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),l=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');M.opts.showChangesEnabled||l.addClass("fr-track-hide"),e.data("tracking-img",!0),_(t).data("tracking")||a.length?(_(n).replaceWith(l),_(l).append(e),M.markers.remove()):(_(n).replaceWith(s),_(s).prepend(l),_(l).append(e),M.markers.remove(),M.track_changes.pushChange(r))}}),M.events.on("commands.before",function(t){if(!M.opts.trackChangesEnabled)return!0;var e=["applytextColor","bold","italic","underline","strikeThrough","subscript","superscript","fontFamily","fontSize","textColor","applybackgroundColor","inlineClass","inlineStyle","alignLeft","alignCenter","formatOLSimple","alignRight","alignJustify","formatOL","formatUL","paragraphFormat","paragraphStyle","lineHeight","outdent","indent","quote"];if(["change","applyAll","applyLast","removeLast","showChanges","trackChanges","moreTrackChanges","undo","redo","fullscreen","print","getPDF","spellChecker","selectAll","html","help"].filter(function(e){return e===t}).length||e.filter(function(e){return e===t}).length){if(e.filter(function(e){return e===t}).length&&"paragraphFormat"!==t&&"paragraphStyle"!==t&&"lineHeight"!==t)p(t);else if("paragraphFormat"===t||"paragraphStyle"===t||"lineHeight"===t){M.selection.save();for(var a=M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),i=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(n,"></span>")),s=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false" style="display:none"></span>'),l=a.parentNode.cloneNode(!0),o=l.childNodes,c=0;c<o.length;)o[c].className&&"fr-marker"===o[c].className?o[c].remove():c++;_(s).append(l),_(i).append(s),_(a.parentNode).after(_("<p></p>")),_(a.parentNode.nextSibling).append(i),i=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(n,"></span>")),_(a.parentNode.nextSibling).after(_("<p></p>")),_(a.parentNode.nextSibling.nextSibling).append(i),i.append(a.parentNode),M.track_changes.pushChange(r),M.selection.restore()}}else{if(U.COMMANDS[t]&&(U.COMMANDS[t].hasOwnProperty("type")&&"dropdown"===U.COMMANDS[t].type||U.COMMANDS[t].more_btn||U.COMMANDS[t].popup)){if("insertImage"!=t&&"insertFile"!=t)return}else if("clearFormatting"===t)return void(M.opts.trackChangesEnabled&&function(){g=M.selection.blocks();for(var a=[],n=0,e=0;e<g.length;e++){n=e<n?n:e;var t=_(g[e]);if(t.is("li"))t.siblings("li").each(function(e,t){_(t).find("li").length&&(a.push({index:n,item:t}),n+=1)});d[e]=_("<span>".concat(t.get(0).innerHTML,"</span>"))}for(var r=0,i=a;r<i.length;r++){var s=i[r],l=s.index,o=s.item,c=_(_(o).get(0).outerHTML);c.find("ul").remove(),c.find("ol").remove(),d=[].concat(j(d.slice(0,l+1)),[_("<span>".concat(c.get(0).innerHTML,"</span>"))],j(d.slice(l+1))),g=[].concat(j(g.slice(0,l+1)),[o],j(g.slice(l+1)))}}());f()}},!0),M.events.on("commands.after",function(e){if(!M.opts.trackChangesEnabled)return!0;if(0<=["undo","redo"].indexOf(e))!function r(){var e=M.$el.find("[data-tracking=true]");s.splice(0,s.length);for(var t=0;t<e.length;t++){var a=_(e.get(t)).data("track-id");s[a.slice(a.lastIndexOf("-")+1)]=a}}();else if("clearFormatting"==e&&M.opts.trackChangesEnabled){var t="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length);for(var a in g){var n=_(g[a]);d[a]&&(k(n,d[a],t),d[a]=null,o[a]=null)}M.track_changes.pushChange(t),d=[],g=null}},!0),M.events.on("quickInsert.commands.before",function(e){M.opts.trackChangesEnabled&&f(e)}),{toggleTracking:function n(){M.opts.trackChangesEnabled&&-1<M.html.get(!0,!0).indexOf("data-tracking")||M.opts.trackChangesEnabled&&0<M.$el.find("[data-tracking=true]").length?alert("Your editor has pending changes. Please resolve them before turning off Track Changes in the toolbar."):(M.commands.moreTrackChanges(),M.opts.trackChangesEnabled=!M.opts.trackChangesEnabled,M.opts.trackChangesEnabled||(M.opts.showChangesEnabled=!1))},pushChange:function r(e){s.push(e)},insertChangeAt:function b(e,t){s=[].concat(j(s.slice(0,e)),[t],j(s.slice(e)))},popChange:c,getPendingChanges:function v(){return s},showChanges:function C(){if(M.opts.showChangesEnabled=!M.opts.showChangesEnabled,function r(e,t){for(var a=0;a<e.length;a++)t?(_(e.get(a)).removeClass("fr-track-hide"),_(e.get(a)).removeClass("fr-track-hide-mobile")):M.helpers.isMobile()?_(e.get(a)).addClass("fr-track-hide-mobile"):_(e.get(a)).addClass("fr-track-hide")}(M.$el.find(".fr-tracking-deleted"),M.opts.showChangesEnabled),M.opts.showChangesEnabled)for(var e=M.$el.find("[data-tracking=true]").not(".fr-highlight-change"),t=0;t<e.length;t++)_(e.get(t)).addClass("fr-highlight-change");else for(var a=M.$el.find(".fr-highlight-change"),n=0;n<a.length;n++)_(a.get(n)).removeClass("fr-highlight-change")},acceptAllChanges:function S(){for(;0!=M.track_changes.getPendingChanges().length;)t();s=[],e(),M.$el.find("li").each(function(){!_(this).children()[0]||"OL"!==_(this).children()[0].tagName&&"UL"!==_(this).children()[0].tagName||e()})},rejectAllChanges:function N(){for(;0!=M.track_changes.getPendingChanges().length;)a();s=[]},acceptSingleChange:t,rejectSingleChange:a,refresh:function E(e){var t=M.$el.find("[data-tracking=true]");e.toggleClass("fr-disabled",!s.length),e.toggleClass("fr-active",s.length).attr("aria-pressed",s.length),0<!t.length&&(e.removeClass("fr-active"),e.addClass("fr-disabled"));var a=M.$tb.find('.fr-command[data-cmd="markdown"]');a&&M.opts.trackChangesEnabled?a.addClass("fr-disabled"):a&&!_(".fr-active.fr-popup").length&&a.removeClass("fr-disabled");var n=M.$tb.find('.fr-command[data-cmd="showChanges"]');n&&M.opts.trackChangesEnabled&&n.removeClass("fr-disabled")},replaceSpecialItem:function y(e){M.selection.setBefore(e.get(0)),M.selection.setAfter(e.get(0)),u(M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),M.$el.find('.fr-marker[data-type="false"]').length&&M.$el.find('.fr-marker[data-type="false"]').get(0))},removeSpecialItem:function x(e){M.selection.setBefore(e.get(0)),M.selection.setAfter(e.get(0)),$(M.$el.find('.fr-marker[data-type="true"]').length&&M.$el.find('.fr-marker[data-type="true"]').get(0),M.$el.find('.fr-marker[data-type="false"]').length&&M.$el.find('.fr-marker[data-type="false"]').get(0))},removedTable:function A(e){var t=!1;if(e.find("[data-tracking=true]").each(function(){0<this.innerText.replace(/\u200B/g,"").length&&(t=!0)}),t)alert("Your table has pending changes. Please resolve them before remove table.");else{var r="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),i=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",a=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),n=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');e.before(a),e.find("tr").find("> td").each(function(){var e=_('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),t=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>'),a=_(this),n=a[0].innerText;_(t)[0].innerText=n,a[0].innerText="",e.prepend(t),a.prepend(e)}),n.append(e),a.append(n),M.track_changes.pushChange(r)}},addQuote:function O(e){var t="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),a=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",n=_('<div data-tracking="true" data-track-id="'.concat(t,'" ').concat(a,"></div>")),r=_('<div data-tracking="true" data-track-id="'.concat(t,'" ').concat(a,"></div>")),i=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');M.opts.showChangesEnabled||i.addClass("fr-track-hide"),e.before(r),e.before(n);var s=e.get(0).innerHTML;i.append(s),n.append(i),r.append(e),r.find('[data-tracking="true"]').each(function(e,t){_(t).children().eq(0).insertBefore(_(t)),_(t).remove()}),M.track_changes.pushChange(t)},removeQuote:function V(e,t){if(!(0<t)){var a="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=_('<div data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></div>")),i=_('<div data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></div>")),s=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');M.opts.showChangesEnabled||s.addClass("fr-track-hide"),e.before(i),e.before(r);var l=e.find("blockQuote").eq(0).html();l||(l=e.html()),s.append(e),r.append(s),i.append(l),i.find('[data-tracking="true"]').each(function(e,t){_(t).children().eq(0).insertBefore(_(t)),_(t).remove()}),M.track_changes.pushChange(a)}},wrapInTracking:function D(e,t){var a="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=_('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>"));t&&r.addClass(t);var i=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');return M.opts.showChangesEnabled||i.addClass("fr-track-hide"),e.before(r),r.append(e),M.track_changes.pushChange(a),r},wrapInDelete:function B(e){var t=_('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');return M.opts.showChangesEnabled||(M.helpers.isMobile()?t.addClass("fr-track-hide-mobile"):t.addClass("fr-track-hide")),t.append(e),t},wrapLinkInTracking:function H(e,t){var a="pending-".concat(M.id,"-").concat(M.track_changes.getPendingChanges().length),n=M.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=_('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>"));return e.before(r),r.append(e),M.track_changes.insertChangeAt(t-1,a),r},pasteInEmptyEdior:function K(e){var t=M.$el.find(".fr-highlight-change");_(t)[0].style.display="block",_(t)[0].innerHTML=e},pasteInEdior:function G(e){var t=M.$el.find(".fr-marker");_(t)[0].outerHTML=e,M.markers.remove()}}},U.DefineIcon("trackChanges",{NAME:"enable-tracking",SVG_KEY:"trackChanges"}),U.DefineIcon("showChanges",{NAME:"show-changes",SVG_KEY:"showTrackChanges"}),U.DefineIcon("applyAll",{NAME:"apply-all",SVG_KEY:"acceptAllChanges"}),U.DefineIcon("removeAll",{NAME:"remove-all",SVG_KEY:"rejectAllChanges"}),U.DefineIcon("applyLast",{NAME:"apply-last",SVG_KEY:"acceptSingleChange"}),U.DefineIcon("removeLast",{NAME:"remove-last",SVG_KEY:"rejectSingleChange"}),U.RegisterCommand("trackChanges",{type:"button",title:"Enable Track Changes",plugin:"track_changes",showOnMobile:!0,callback:function(){this.track_changes.toggleTracking()},refreshAfterCallback:!0,forcedRefresh:!0,refresh:function(e){if(e){var t=this.$tb.find('.fr-more-toolbar[data-name="'.concat(e.attr("id"),'"]')),a=0!==t.length&&t.hasClass("fr-expanded");(this.opts.trackChangesEnabled&&this.opts.toolbarContainer&&!a||!this.opts.trackChangesEnabled&&this.opts.toolbarContainer&&a)&&(this.$tb.find('.fr-more-toolbar[data-name="'.concat(e.attr("id"),'"]')).toggleClass("fr-expanded"),this.$box.toggleClass("fr-toolbar-open"),this.$tb.toggleClass("fr-toolbar-open"),e.toggleClass("fr-open")),e.toggleClass("fr-active",this.opts.trackChangesEnabled).attr("aria-pressed",this.opts.trackChangesEnabled)}}}),U.RegisterCommand("showChanges",{type:"button",icon:"showChanges",title:"Show Changes",plugin:"track_changes",undo:!1,focus:!1,accessibilityFocus:!0,forcedRefresh:!0,refreshAfterCallback:!0,toggle:!0,callback:function(){this.track_changes.showChanges()},refresh:function(e){e.toggleClass("fr-active",this.opts.showChangesEnabled).attr("aria-pressed",this.opts.showChangesEnabled)}}),U.RegisterCommand("applyAll",{type:"button",icon:"applyAll",title:"Accept All Changes",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.acceptAllChanges()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),U.RegisterCommand("removeAll",{type:"button",icon:"removeAll",title:"Reject All Changes",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.rejectAllChanges()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),U.RegisterCommand("applyLast",{type:"button",icon:"applyLast",title:"Accept Single Change",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.acceptSingleChange()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),U.RegisterCommand("removeLast",{type:"button",icon:"removeLast",title:"Reject Single Change",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.rejectSingleChange()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}})});