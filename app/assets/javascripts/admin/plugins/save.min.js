/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(e){"use strict";e=e&&e.hasOwnProperty("default")?e["default"]:e,Object.assign(e.DEFAULTS,{saveInterval:1e4,saveURL:null,saveParams:{},saveParam:"body",saveMethod:"POST"}),e.PLUGINS.save=function(i){var f=i.$,n=null,l=null,t=!1,v=1,c=2,s={};function u(e,t){i.events.trigger("save.error",[{code:e,message:s[e]},t])}function a(e){void 0===e&&(e=i.html.get());var n=e,t=i.events.trigger("save.before",[e]);if(!1===t)return!1;if("string"==typeof t&&(e=t),i.opts.saveURL){var s={};for(var a in i.opts.saveParams)if(i.opts.saveParams.hasOwnProperty(a)){var o=i.opts.saveParams[a];s[a]="function"==typeof o?o.call(this):o}var r={};r[i.opts.saveParam]=e,f(this).ajax({method:i.opts.saveMethod,url:i.opts.saveURL,data:Object.assign(r,s),crossDomain:i.opts.requestWithCORS,withCredentials:i.opts.requestWithCredentials,headers:i.opts.requestHeaders,done:function(e,t,s){l=n,i.events.trigger("save.after",[e])},fail:function(e){u(c,e.response||e.responseText)}})}else u(v)}function o(){clearTimeout(n),n=setTimeout(function(){var e=i.html.get();(l!=e||t)&&(t=!1,a(l=e))},0)}return s[v]="Missing saveURL option.",s[c]="Something went wrong during save.",{_init:function r(){if(i.opts.letteringClass)for(var e=i.opts.letteringClass,t=i.$el.find(".".concat(e)).length,s=0;s<t;s++)i.$el.find(".".concat(e))[s].innerHTML=i.$el.find(".".concat(e))[s].innerText.replace(/([\w'-]+|[?.",])/g,"<span class = 'fr-word-select'>$1</span>");i.opts.saveInterval&&(l=i.html.get(),i.events.on("contentChanged",function(){setTimeout(o,i.opts.saveInterval)}),i.events.on("keydown destroy",function(){clearTimeout(n)}))},save:a,reset:function e(){o(),t=!1},force:function d(){t=!0}}},e.DefineIcon("save",{NAME:"floppy-o",FA5NAME:"save"}),e.RegisterCommand("save",{title:"Save",undo:!1,focus:!1,refreshAfterCallback:!1,callback:function(){this.save.save()},plugin:"save"})});