/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(a,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],e):e(a.FroalaEditor)}(this,function(a){"use strict";a=a&&a.hasOwnProperty("default")?a["default"]:a,Object.assign(a.DEFAULTS,{paragraphStyles:{"fr-text-gray":"Gray","fr-text-bordered":"Bordered","fr-text-spaced":"Spaced","fr-text-uppercase":"Uppercase"},paragraphMultipleStyles:!0}),a.PLUGINS.paragraphStyle=function(i){var p=i.$;return{_init:function a(){},apply:function c(a,e,t){void 0===e&&(e=i.opts.paragraphStyles),void 0===t&&(t=i.opts.paragraphMultipleStyles);var r="";t||((r=Object.keys(e)).splice(r.indexOf(a),1),r=r.join(" ")),i.selection.save(),i.html.wrap(!0,!0,!0,!0),i.selection.restore();var l=i.selection.blocks();i.selection.save();for(var n=p(l[0]).hasClass(a),s=0;s<l.length;s++)p(l[s]).removeClass(r).toggleClass(a,!n),p(l[s]).hasClass("fr-temp-div")&&p(l[s]).removeClass("fr-temp-div"),""===p(l[s]).attr("class")&&p(l[s]).removeAttr("class");if(1===l.length&&"fr-text-bordered"===a&&null===l[0].nextSibling){var o=p("<br>");l[0].after(o[0])}i.html.unwrap(),i.selection.restore()},refreshOnShow:function l(a,e){var t=i.selection.blocks();if(t.length){var r=p(t[0]);e.find(".fr-command").each(function(){var a=p(this).data("param1"),e=r.hasClass(a);p(this).toggleClass("fr-active",e).attr("aria-selected",e)})}}}},a.RegisterCommand("paragraphStyle",{type:"dropdown",html:function(){var a='<ul class="fr-dropdown-list" role="presentation">',e=this.opts.paragraphStyles;for(var t in e)e.hasOwnProperty(t)&&(a+='<li role="presentation"><a class="fr-command '.concat(t,'" tabIndex="-1" role="option" data-cmd="paragraphStyle" data-param1="').concat(t,'" title="').concat(this.language.translate(e[t]),'">').concat(this.language.translate(e[t]),"</a></li>"));return a+="</ul>"},title:"Paragraph Style",callback:function(a,e){this.paragraphStyle.apply(e)},refreshOnShow:function(a,e){this.paragraphStyle.refreshOnShow(a,e)},plugin:"paragraphStyle"}),a.DefineIcon("paragraphStyle",{NAME:"magic",SVG_KEY:"paragraphStyle"})});