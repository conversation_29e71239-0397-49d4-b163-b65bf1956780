/*!
 * froala_editor v4.0.13 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(l){"use strict";l=l&&l.hasOwnProperty("default")?l["default"]:l,Object.assign(l.DEFAULTS,{lineHeights:{Default:"",Single:"1",1.15:"1.15",1.5:"1.5",Double:"2"}}),l.PLUGINS.lineHeight=function(n){var o=n.$;return{_init:function e(){},apply:function a(e){n.selection.save(),n.html.wrap(!0,!0,!0,!0),n.selection.restore();var t=n.selection.blocks();t.length&&o(t[0]).parent().is("td")&&n.format.applyStyle("line-height",e.toString()),n.selection.save();for(var i=0;i<t.length;i++)o(t[i]).css("line-height",e),n.opts.enter!==l.ENTER_BR||t.length&&o(t[0]).parent().is("td")||(o(t[i]).hasClass("fr-temp-div")&&o(t[i]).removeClass("fr-temp-div"),""===o(t[i]).attr("class")&&o(t[i]).removeAttr("class")),""===o(t[i]).attr("style")&&o(t[i]).removeAttr("style");n.html.unwrap(),n.selection.restore()},refreshOnShow:function r(e,t){var i=n.selection.blocks();if(i.length){var l=o(i[0]);t.find(".fr-command").each(function(){var e=o(this).data("param1"),t=l.attr("style"),i=0<=(t||"").indexOf("line-height: "+e+";");if(t){var n=t.substring(t.indexOf("line-height")),a=n.substr(0,n.indexOf(";")),r=a&&a.split(":")[1];r&&r.length||"Default"!==l.text()||(i=!0)}t&&-1!==t.indexOf("line-height")||""!==e||(i=!0),o(this).toggleClass("fr-active",i).attr("aria-selected",i)})}}}},l.RegisterCommand("lineHeight",{type:"dropdown",html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.lineHeights;for(var i in t)t.hasOwnProperty(i)&&(e+='<li role="presentation"><a class="fr-command '.concat(i,'" tabIndex="-1" role="option" data-cmd="lineHeight" data-param1="').concat(t[i],'" title="').concat(this.language.translate(i),'">').concat(this.language.translate(i),"</a></li>"));return e+="</ul>"},title:"Line Height",callback:function(e,t){this.lineHeight.apply(t)},refreshOnShow:function(e,t){this.lineHeight.refreshOnShow(e,t)},plugin:"lineHeight"}),l.DefineIcon("lineHeight",{NAME:"arrows-v",FA5NAME:"arrows-alt-v",SVG_KEY:"lineHeight"})});