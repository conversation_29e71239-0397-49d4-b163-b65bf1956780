# == Schema Information
#
# Table name: permission_actions
#
#  id                                 :bigint           not null, primary key
#  deleted_at(删除时间)               :datetime
#  name(动作名称)                     :string
#  order_number(排序)                 :integer
#  word(别名)                         :string
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  organization_id(组织ID)            :integer
#  permission_controller_id(控制器ID) :integer
#
# Indexes
#
#  index_permission_actions_on_deleted_at                (deleted_at)
#  index_permission_actions_on_organization_id           (organization_id)
#  index_permission_actions_on_permission_controller_id  (permission_controller_id)
#
class PermissionAction < ApplicationRecord
  belongs_to :organization
  belongs_to :permission_controller
end
