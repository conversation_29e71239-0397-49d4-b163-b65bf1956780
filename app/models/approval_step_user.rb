# == Schema Information
#
# Table name: approval_step_users
#
#  id                                                           :bigint           not null, primary key
#  deleted_at(删除时间)                                         :datetime
#  order_number(步骤顺序)                                       :integer
#  review_comment(审核意见)                                     :string
#  status(状态(1: 等待中, 2: "进行中", 3: "已完成", 4: "驳回")) :integer
#  created_at                                                   :datetime         not null
#  updated_at                                                   :datetime         not null
#  approval_step_id(审核步骤ID)                                 :integer
#  organization_id(组织ID)                                      :integer
#  user_id(审核人用户ID)                                        :integer
#
# Indexes
#
#  index_approval_step_users_on_approval_step_id  (approval_step_id)
#  index_approval_step_users_on_organization_id   (organization_id)
#  index_approval_step_users_on_user_id           (user_id)
#
class ApprovalStepUser < ApplicationRecord
  acts_as_paranoid

  belongs_to :approval_step
  belongs_to :organization
  belongs_to :user

  # 等待中 1, 进行中 2, 已完成 3, 驳回 4
  enum status: {waiting: 1, in_progress: 2, approved: 3, rejected: 4}

  after_save :next_proccess_approval

  def status_color
    case status
    when 'waiting'
      ['', ""]
    when 'in_progress'
      ['#1e9fff', '<i class="layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop"></i>']
    when 'approved'
      ['#16b777', '<i class="layui-icon layui-icon-success"></i>']
    when 'rejected'
      ['#ff5722', '<i class="layui-icon layui-icon-error"></i>']
    else
      ['', ""]
    end
  end

  def get_button
    buttons = []
    if self.in_progress?
      buttons = [{id: "approved", title: '通过'}, {id: 'rejected', title: '驳回'}]
    end
    return buttons
  end

  private
  def next_proccess_approval
    change_commend = self.previous_changes
    return unless change_commend.has_key?(:status)
    statuses = self.approval_step.approval_step_users.pluck(:status).uniq

    if self.approval_step.counter_sign? # 会签（需所有审批人同意)
      self.approval_step.update!(status: 'approved') if statuses.all?('approved') # 所有审批人同意
      self.approval_step.update!(status: 'rejected') if statuses.include?('rejected') # 只要有一名审批人驳回，则当前步骤驳回
    elsif self.approval_step.or_sign? # 或签(一名审批人同意即可)
      self.approval_step.update!(status: 'approved') if statuses.include?('approved') # 只要有一名审批人同意即可
      self.approval_step.update!(status: 'rejected') if statuses.all?('rejected')
    elsif self.approval_step.serial_sign? # 依次审批（按顺序依次审批）
      if change_commend[:status].last == 'approved'
        next_step = self.approval_step.approval_step_users.find_by(order_number: self.order_number + 1)
        if next_step.present?
          next_step.update!(status: 'in_progress') # 更新下一个步骤为进行中
        else
          self.approval_step.update!(status: 'approved') # 如果没有下一个步骤，则当前步骤完成
        end
      elsif change_commend[:status].last == 'rejected'
        self.approval_step.update!(status: 'rejected') # 如果当前步骤被驳回，则当前步骤驳回
      end
    end

    self.approval_step.begin_next if self.approval_step.approved? || self.approval_step.rejected?
  end
end
