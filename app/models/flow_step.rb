# == Schema Information
#
# Table name: flow_steps
#
#  id                                                                                                            :bigint           not null, primary key
#  deleted_at(删除时间)                                                                                          :datetime
#  name(步骤名称)                                                                                                :string
#  order_number(步骤顺序)                                                                                        :integer
#  review_type(审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）) :integer
#  review_user_ids(审核人用户ID列表)                                                                             :string
#  created_at                                                                                                    :datetime         not null
#  updated_at                                                                                                    :datetime         not null
#  flow_version_id(流程版本ID)                                                                                   :integer
#  organization_id(组织ID)                                                                                       :integer
#
# Indexes
#
#  index_flow_steps_on_flow_version_id  (flow_version_id)
#  index_flow_steps_on_organization_id  (organization_id)
#
class FlowStep < ApplicationRecord
  acts_as_paranoid

  belongs_to :flow_version

  validates :order_number, :name, presence: true

  attr_accessor :copy_review_user_ids

  # counter_sign: 会签(需所有审批人同意) or_sign: 或签(一名审批人同意即可) serial_sign: 依次审批（按顺序依次审批）
  enum review_type: {counter_sign: 1, or_sign: 2, serial_sign: 3 }

  validates :review_type, :name, :order_number, :review_user_ids, presence: true

  def select_review_user_ids
    return [] if self.review_user_ids.blank?

    ids_array = self.review_user_ids.split(',').map(&:to_i)

    users = User.where(id: ids_array)
            .order(Arel.sql("array_position(ARRAY[#{ids_array.join(',')}], id::int)"))
  end

  def xm_data_users
    users = self.select_review_user_ids
    return [] if users.blank?
    result = users.map do |user|
      {
        name: user.name,
        value: user.id,
        selected: true
      }
    end
    result
  end
end
