# == Schema Information
#
# Table name: organization_permission_controllers
#
#  id                                     :bigint           not null, primary key
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  organization_id(企业ID)                :integer
#  permission_controller_id(权限控制器ID) :integer
#
# Indexes
#
#  index_org_perm_ctrl_on_org_id                   (organization_id)
#  index_org_perm_ctrl_on_org_id_and_perm_ctrl_id  (organization_id,permission_controller_id) UNIQUE
#  index_org_perm_ctrl_on_perm_ctrl_id             (permission_controller_id)
#
class OrganizationPermissionController < ApplicationRecord
  belongs_to :organization
  belongs_to :permission_controller

  validates :organization_id, presence: true
  validates :permission_controller_id, presence: true
  validates :organization_id, uniqueness: { scope: :permission_controller_id }
end
