# == Schema Information
#
# Table name: work_order_reason_logs
#
#  id                    :bigint           not null, primary key
#  deleted_at(删除时间)  :datetime
#  reason(理由)          :string
#  reason_type(理由类型) :integer
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  work_order_id(工单Id) :integer
#
# Indexes
#
#  index_work_order_reason_logs_on_work_order_id  (work_order_id)
#
class WorkOrderReasonLog < ApplicationRecord
  belongs_to :work_order

  #理由类型
  #驳回理由 、 解决方案 、 客户确认、 客户手动关闭、超时自动关闭 、 解决关闭
  enum :reason_type, {rejection: 1, t_solution: 2, confirm_closed: 3, manual_closed: 4, timeout_closed: 5, already_solved: 6}
end
