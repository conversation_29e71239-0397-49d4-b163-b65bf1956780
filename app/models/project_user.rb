# == Schema Information
#
# Table name: project_users
#
#  id                                       :bigint           not null, primary key
#  deleted_at(删除时间)                     :datetime
#  created_at                               :datetime         not null
#  updated_at                               :datetime         not null
#  organization_id(外键: 组织id)            :integer # 这里是用户归属的组织id，或者是配置的组织ID
#  project_id(外键: 项目id)                 :integer
#  project_role_config_id(外键: 角色配置ID) :integer
#  user_id(外键: 用户id)                    :integer
#
# Indexes
#
#  index_project_users_on_organization_id         (organization_id)
#  index_project_users_on_project_id              (project_id)
#  index_project_users_on_project_role_config_id  (project_role_config_id)
#  index_project_users_on_user_id                 (user_id)
#
class ProjectUser < ApplicationRecord
  acts_as_paranoid

  belongs_to :organization
  belongs_to :project
  belongs_to :user
  belongs_to :project_role_config

  # 验证在同一个 project_id 下 user_id 是唯一的
  validates :user_id, uniqueness: { scope: :project_id, message: '在一个项目中不能重复添加同一个用户' }
end
