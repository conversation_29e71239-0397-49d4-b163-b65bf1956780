# == Schema Information
#
# Table name: chip_configs
#
#  id                              :bigint           not null, primary key
#  c_type(芯片类型)                :integer
#  code(芯片编码)                  :string
#  deleted_at                      :datetime
#  description(描述)               :string
#  name(芯片配置名称)              :string
#  product_line(产品线)            :integer
#  status(状态)                    :boolean          default(TRUE)
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  organization_id                 :integer
#  product_category_id(产品类型ID) :integer
#
# Indexes
#
#  index_chip_configs_on_organization_id  (organization_id)
#
class ChipConfig < ApplicationRecord
  acts_as_paranoid

  belongs_to :organization
  belongs_to :product_category

  has_many :chip_os_softwares, dependent: :destroy

  validates :name, :code, :c_type, :product_line, :status, presence: true
  validates :code, uniqueness: { scope: :organization_id, message: '编码已存在' }

  # 产品线: K系列/ P系列/V系列
  enum product_line: {k_series: 1, p_series: 2, v_series: 3}

  # 芯片类型: 通用芯片/电源芯片/定制芯片
  enum c_type: {c_universal: 1, c_power: 2, c_custom: 3}

end
