# == Schema Information
#
# Table name: project_role_configs
#
#  id                            :bigint           not null, primary key
#  deleted_at(删除时间)          :datetime
#  is_default(是否默认角色)      :boolean          default(FALSE)
#  name(角色名称)                :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  organization_id(外键: 组织id) :integer
#
# Indexes
#
#  index_project_role_configs_on_organization_id  (organization_id)
#
class ProjectRoleConfig < ApplicationRecord
  acts_as_paranoid
  # belongs_to :organization
  has_many :project_role_permissions, dependent: :destroy
  has_many :project_permission_configs, through: :project_role_permissions

  validates :name, presence: true

  def self.array_list(organization_id)
    ProjectRoleConfig.where(organization_id: organization_id).order(:created_at).pluck(:name, :id)
  end
end
