module AasmWorkOrder
  extend ActiveSupport::Concern

  included  do
    include AASM

    ##
    # @class Aasm_cash_audit
    # @Description:
    # (1) not_started 新建：
    # (2) evaluating 评估中：
    # (3) in_progress 进行中：
    # (4) solved 已解决：
    # (6) close 关闭：
    # @params:
    # @return: nil
    aasm do
      state :not_started, :initial => true
      state :evaluating, :in_progress, :solved, :close

      # 新建 -> 评估中
      event :evaluate do
        transitions :from => :not_started, :to => :evaluating, :after => :send_evaluating_email
      end

      # 评估中 -> 进行中
      event :in_progress do
        transitions :from => :evaluating, :to => :in_progress, :after => :notice_obligation_user
      end

      # 已解决 -> 进行中（未解决此任务情况）
      event :review_failed do
        transitions :from => :solved, :to => :in_progress, :after => :send_notice_user
      end

      # 进行中 -> 已解决
      event :solved do
        transitions :from => :in_progress, :to => :solved, :after => :notice_founder
      end

      # 再打开
      # 关闭 -> 评估中
      event :reopened do
        transitions :from => [:close], :to => :evaluating, :after => :update_act_at
      end

      # (评估中 | 进行中) -> 关闭
      event :closed do
        transitions :from => [:evaluating, :in_progress, :solved], :to => :close
      end

    end
  end

  def send_evaluating_email
    content = "您有新的工单待评估，请及时查看"
    UserNotice.init_message!(self.receiver_user.organization_id, self.receiver_user_id, self, "#{self.work_type_i18n}工单评估通知", content, self.notice_url)
  end

  def notice_obligation_user
    content = "您有新的工单待处理，请及时查看"
    UserNotice.init_message!(self.obligation_user.organization_id, self.obligation_user_id, self, "#{self.work_type_i18n}工单开展通知", content, self.notice_url)
  end

  def notice_founder
    content = "您发起的工单: #{self.title} 已经解决，请前往查看"
    UserNotice.init_message!(self.founder.organization_id, self.founder_id, self, "#{self.work_type_i18n}工单进展通知", content, self.notice_url)
  end

  def send_notice_user
    content = "您处理的工单: #{self.title} 被驳回了，驳回原因: #{self.customer_confirmation}"
    UserNotice.init_message!(self.obligation_user.organization_id, self.obligation_user_id, self, "#{self.work_type_i18n}工单驳回通知", content, self.notice_url)
  end

  def update_act_at
    content = "您有新的工单被重新打开，等待评估，请及时查看"
    UserNotice.init_message!(self.receiver_user.organization_id, self.receiver_user_id, self, "#{self.work_type_i18n}工单评估通知", content, self.notice_url)
    self.update!(act_started_at: nil, act_ended_at: nil, closed_at: nil)
  end

end
