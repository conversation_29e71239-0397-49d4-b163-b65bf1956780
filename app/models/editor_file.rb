# == Schema Information
#
# Table name: editor_files
#
#  id         :bigint           not null, primary key
#  file       :string
#  file_name  :string
#  file_type  :integer
#  url        :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class EditorFile < ApplicationRecord
  enum file_type: {image: 1, video: 2, file_other: 3}

  mount_uploader :file, EditorFileFileUploader

  after_commit :generate_url, on: :create

  private
  def generate_url
    self.update(url: self.reload.file_url)
  end
end
