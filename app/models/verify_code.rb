# == Schema Information
#
# Table name: verify_codes
#
#  id         :bigint           not null, primary key
#  code       :string(50)
#  deleted_at :datetime
#  expired_at :datetime
#  phone      :string(50)
#  used_at    :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class VerifyCode < ApplicationRecord
  acts_as_paranoid

  def self.send_sms phone
    code = generate_verify_code
    self.create(code: code, phone: phone, expired_at: Time.now + 5.minutes)
    #发送消息
    if Rails.env.production?
      param_string = {'code' => code}.to_json
      sms_result = Aliyun::Sms.send(phone, 'SMS_186470163', param_string)
    end
  end

  def self.generate_verify_code len = 6
    a = lambda { (1..9).to_a.sample }
    code = ""
    len.times { |t| code << a.call.to_s }
    code
  end

end
