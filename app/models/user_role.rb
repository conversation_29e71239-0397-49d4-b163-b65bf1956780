# == Schema Information
#
# Table name: user_roles
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#  role_id(角色ID)         :integer
#  user_id(用户ID)         :integer
#
# Indexes
#
#  index_user_roles_on_deleted_at       (deleted_at)
#  index_user_roles_on_organization_id  (organization_id)
#  index_user_roles_on_role_id          (role_id)
#  index_user_roles_on_user_id          (user_id)
#
class UserRole < ApplicationRecord
  belongs_to :user
  belongs_to :role
end
