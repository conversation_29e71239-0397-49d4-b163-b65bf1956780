# == Schema Information
#
# Table name: organization_flows
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  description(流程描述)   :string
#  flow_type(使用类型)     :integer
#  name(流程名称)          :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#  user_id(用户ID)         :integer
#
# Indexes
#
#  index_organization_flows_on_organization_id  (organization_id)
#  index_organization_flows_on_user_id          (user_id)
#
class OrganizationFlow < ApplicationRecord
  acts_as_paranoid

  belongs_to :organization
  belongs_to :user
  has_many :flow_versions, dependent: :destroy

  validates :name, :flow_type, presence: true

  # 项目(内部方案: 1, 技术支持案: 2, 联合开发案: 3) 工单  组织申请
  enum flow_type: {f_project: 1, f_project_support: 2, f_project_joint: 3, f_organization: 4 }

  # 默认使用的版本
  def used_version
    self.flow_versions.find_by(status: true)&.version
  end

  def default_flow_version
    self.flow_versions.find_by(status: true) || self.flow_versions.order(:created_at).last
  end

end
