class Web::SessionsController < ApplicationController
  # 登陆页
  def index
  end

  # 注册创建
  def create
  end

  # 验证登陆页
  def login_user
    current_user = User.find_by(phone: params[:phone])
    if current_user && current_user.authenticate(params[:password])
      # 用户登录成功
      flash[:success] = '登陆成功'
      session[:user_id] = current_user.id
      redirect_to web_homes_path
    else
      flash[:error] = '密码错误'
      redirect_to web_sessions_path(phone: params[:phone])
    end
  end

  # 注销登陆
  def logout
    flash[:success] = '注销成功'
    session[:user_id] = nil if session[:user_id].present?
    redirect_to web_sessions_path
  end

  # # 忘记密码
  # def forgot_password
  # end

  # def changepassword
  #   @current_user = User.find_by(id: session[:user_id])
  # end

  # # 注册账号
  # def register_account
  # end

  # def registered
  #   begin
  #     raise '请勾选协议' unless params[:user][:agreement] == 'on'
  #     raise '手机号已经被注册' if User.exists?(phone: params[:user][:phone])
  #     User.create!(user_params)
  #     render json: {status: true}
  #   rescue => exception
  #     render json: {status: false, msg: exception.message}
  #   end

  # end
  private
  def user_params
    params.require(:user).permit(:name, :phone, :password, :password_confirmation, :avatar)
  end

  def sign_params
    params.permit(:login, :password)
  end
end
