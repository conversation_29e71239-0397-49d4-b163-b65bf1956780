class Web::ApplicationController < ActionController::Base
  before_action :check_user
  helper_method :current_user, :current_user_id, :current_ai_chat

  # 后台登陆用户
  def current_user
    if session[:user_id]
      @current_user ||= User.find_by(id: session[:user_id])
    end
  end

  def current_user_id
    current_user.id
  end

  def current_ai_chat
    if session[:ai_chat_id]
      @current_ai_chat ||= AiChat.find_by(id: session[:ai_chat_id])
    end
  end

  # 验证用户是否登陆
  def check_user
   redirect_to web_sessions_path and return if current_user.blank?
  end
end
