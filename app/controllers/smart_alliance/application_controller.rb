class SmartAlliance::ApplicationController < ActionController::Base
  include Pundit
  include Pundit::Authorization
  before_action :check_user
  helper_method :current_user, :current_user_id, :current_organization

  # 后台登陆用户
  def current_user
    if session[:user_id]
      @user ||= User.find_by(id: session[:user_id])
    end
  end

  def current_user_id
    current_user.id
  end

  def current_organization
    current_user.organization
  end

  # 验证用户是否登陆
  def check_user
   redirect_to admin_sessions_path and return if current_user.blank?
  end
end
