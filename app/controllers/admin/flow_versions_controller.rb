class Admin::FlowVersionsController < Admin::ApplicationController

  before_action do
    authorize FlowVersion
  end
  before_action :set_flow_version, only: [:show, :edit, :update, :destroy]

  # GET /admin/flow_versions
  def index
    @flow_versions = FlowVersion.all
  end

  # GET /admin/flow_versions/1
  def show
  end

  # GET /admin/flow_versions/new
  def new
    @flow_version = FlowVersion.new
  end

  # GET /admin/flow_versions/1/edit
  def edit
    flash[:notice] = "已切换到V#{@flow_version.version}"
  end

  # POST /admin/flow_versions
  def create
    version = FlowVersion.where(organization_flow_id: params[:organization_flow_id]).maximum(:version).to_i + 1
    @flow_version = FlowVersion.new(organization_flow_id: params[:organization_flow_id], status: false, version: version, user_id: current_user_id, organization_id: current_organization_id)
    @status = @flow_version.save
    flash[:success] = "创建成功"
  end

  # PATCH/PUT /admin/flow_versions/1
  def update
    begin
      ActiveRecord::Base.transaction do
        @status = @flow_version.update(flow_version_params)
        notice = '保存成功'
        if @flow_version.status
          notice = '发布成功'
          @flow_version.organization_flow.flow_versions.where.not(id: @flow_version.id).update_all(status: false)
        end
        flash[:success] = notice if @status
      end
    rescue => exception
      @status = false
      flash[:error] = "操作失败: #{exception.message}"
    end

  end

  # DELETE /admin/flow_versions/1
  def destroy
    @flow_version.destroy
    redirect_to admin_flow_versions_url, notice: 'Flow version was successfully destroyed.'
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_flow_version
      @flow_version = FlowVersion.find(params[:id])
    end

    # Only allow a trusted parameter "white list" through.
    def flow_version_params
      params.require(:flow_version).permit(:version, :status, :organization_flow_id, :organization_id, :user_id,
        flow_steps_attributes: [:id, :order_number, :name, :review_type, :review_user_ids, :flow_version_id, :organization_id, :_destroy]
      )
    end
end
