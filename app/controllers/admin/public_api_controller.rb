class Admin::PublicApiController < Admin::ApplicationController
  before_action do
    authorize "PublicApi", policy_class: PublicApiPolicy
  end
  # 所有内容为公共 API 接口 都需要有权限

  def get_chip_configs
    chip_configs = ChipConfig.where(organization_id: params[:organization_id])
    result = chip_configs.order(created_at: :desc).map do |chip_config|
      {
        name: chip_config.name,
        value: chip_config.id
      }
    end
    render json: {status: true, data: result}
  end

  def get_chip_sofwares
    chip_config = ChipConfig.find_by(id: params[:chip_config_id])
    result = chip_config.chip_os_softwares.order(created_at: :desc).map do |chip_os_software|
      {
        name: chip_os_software.name,
        value: chip_os_software.id
      }
    end
    render json: {status: true, data: result}
  end

  def get_chip_versions
    chip_os_software = ChipOsSoftware.find_by(id: params[:chip_os_software_id])
    result = chip_os_software.chip_os_versions.order(is_default: :desc, created_at: :desc).map do |chip_os_version|
      name = chip_os_version.is_default ? "#{chip_os_version.version}(默认)" : chip_os_version.version
      {
        name: name,
        value: chip_os_version.id
      }
    end
    render json: {status: true, data: result}
  end

  def search_users
    result = current_organization.users.ransack({name_or_phone_cont: params[:q]}).result.order(created_at: :desc).map do |user|
      {
        name: user.name,
        value: user.id
      }
    end
    render json: {status: true, data: result}
  end

  def search_project_users
    project = current_organization.projects.find(params[:project_id])
    result = project.current_users(current_organization_id).order(created_at: :desc).map do |user|
      {
        name: user.name,
        value: user.id
      }
    end
    render json: {status: true, data: result}
  end
end
