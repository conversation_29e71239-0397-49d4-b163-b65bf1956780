class Admin::OrganizationFlowsController < Admin::ApplicationController
  before_action do
    authorize OrganizationFlow
  end

  before_action :set_organization_flow, only: [:show, :edit, :update, :destroy]

  # GET /admin/organization_flows
  def index
    if request.xhr?
      @organization_flows = OrganizationFlow.where(organization_id: current_organization_id)
      result = @organization_flows.includes(:flow_versions).page(params[:page]).per(params[:limit]).map do |organization_flow|
        {
          id: organization_flow.id,
          name: organization_flow.name,
          description: organization_flow.description,
          flow_type: organization_flow.flow_type_i18n,
          used_version: organization_flow.used_version.present? ? "V#{organization_flow.used_version}" : '',
          flow_version_id: organization_flow.default_flow_version.id,
          username: organization_flow.user.name
        }
      end
      render json: {code: 0, data: result, count: @organization_flows.count}
    end
  end

  # GET /admin/organization_flows/1
  def show
  end

  # GET /admin/organization_flows/new
  def new
    @organization_flow = OrganizationFlow.new(user_id: current_user_id, organization_id: current_organization_id)
  end

  # GET /admin/organization_flows/1/edit
  def edit
  end

  # POST /admin/organization_flows
  def create
    @organization_flow = OrganizationFlow.new(organization_flow_params)
    @organization_flow.flow_versions.new(status: false, version: 0, user_id: current_user_id, organization_id: current_organization_id)
    @status = @organization_flow.save
  end

  # PATCH/PUT /admin/organization_flows/1
  def update
    @status = @organization_flow.update(organization_flow_params)
  end

  # DELETE /admin/organization_flows/1
  def destroy
    @status = @organization_flow.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_organization_flow
      @organization_flow = OrganizationFlow.find_by(id: params[:id], organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def organization_flow_params
      params.require(:organization_flow).permit(:name, :description, :flow_type, :organization_id, :user_id)
    end
end
