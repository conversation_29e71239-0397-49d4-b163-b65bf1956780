class Admin::ChipOsVersionsController < Admin::ApplicationController
  before_action do
    authorize ChipOsVersion
  end

  before_action :set_chip_os_version, only: [:show, :edit, :update, :destroy]

  # GET /admin/chip_os_versions
  def index
    chip_os_software = ChipOsSoftware.find_by(id: params[:chip_os_software_id], organization_id: current_organization_id)
    @chip_os_versions = chip_os_software.chip_os_versions.order(is_default: :desc, created_at: :desc)
    if request.xhr? && params[:open].blank?
      @result = @chip_os_versions.page(params[:page]).per(params[:limit]).map do |chip_os_version|
        {
          id: chip_os_version.id,
          version: chip_os_version.version,
          is_default: chip_os_version.is_default ? '是' : '否'
        }
      end
      render json: {code: 0, data: @result, count: @chip_os_versions.count}
    end
  end

  # GET /admin/chip_os_versions/1
  def show
  end

  # GET /admin/chip_os_versions/new
  def new
    @chip_os_version = ChipOsVersion.new(chip_os_software_id: params[:chip_os_software_id], organization_id: current_organization_id)
  end

  # GET /admin/chip_os_versions/1/edit
  def edit
  end

  # POST /admin/chip_os_versions
  def create
    @chip_os_version = ChipOsVersion.new(chip_os_version_params)
    begin
      ActiveRecord::Base.transaction do
        ChipOsVersion.where(chip_os_software_id: @chip_os_version.chip_os_software_id, organization_id: current_organization_id).update_all(is_default: false) if @chip_os_version.is_default
        @chip_os_version.save!
      end
      @status = true
    rescue => exception
      Rails.logger.error "#{exception.message}"
      @status = false
    end
  end

  # PATCH/PUT /admin/chip_os_versions/1
  def update
    if @chip_os_version.update(chip_os_version_params)
      ChipOsVersion.where.not(id: @chip_os_version.id, chip_os_software_id: @chip_os_version.chip_os_software_id, organization_id: current_organization_id).update_all(is_default: false) if @chip_os_version.is_default
      @status = true
    else
      @status = false
    end
  end

  # DELETE /admin/chip_os_versions/1
  def destroy
    @status = @chip_os_version.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_chip_os_version

      @chip_os_version = ChipOsVersion.find_by(id: params[:id], organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def chip_os_version_params
      params.require(:chip_os_version).permit(:is_default, :version, :chip_os_software_id, :organization_id)
    end
end
