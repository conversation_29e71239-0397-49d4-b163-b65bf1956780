class Admin::ProjectRoleConfigsController < Admin::ApplicationController
  before_action do
    authorize ProjectRoleConfig
  end

  before_action :set_project_role_config, only: [:show, :edit, :update, :destroy, :update_is_default]

  # GET /admin/project_role_configs
  def index
    @project_role_configs = ProjectRoleConfig.where(organization_id: current_organization_id).order(:created_at)
    if request.xhr?
      @result = @project_role_configs.map do |project_role_config|
        {
          id: project_role_config.id,
          name: project_role_config.name,
          is_default: project_role_config.is_default || false,
          permissions: project_role_config.project_permission_configs.pluck(:name).join('、')
        }
      end
      render json: {code: 0, msg: 'success', data: @result}
    end
  end

  # GET /admin/project_role_configs/1
  def show
  end

  # GET /admin/project_role_configs/new
  def new
    @project_role_config = ProjectRoleConfig.new(organization_id: current_organization_id)
    @select_data = []
    ProjectPermissionConfig.where(parent_id: nil, organization_id: current_organization_id).each do |project_permission_config|
      hash = {
        name: project_permission_config.name,
        children: []
      }
      hash[:children] = project_permission_config.child_project_permission_configs.where(organization_id: current_organization_id).map do |child_project_permission_config|
        {
          name: child_project_permission_config.name,
          value: child_project_permission_config.id
        }
      end
      @select_data << hash
    end
  end

  # GET /admin/project_role_configs/1/edit
  def edit
    @select_data = []
    ProjectPermissionConfig.where(parent_id: nil, organization_id: current_organization_id).each do |project_permission_config|
      hash = {
        name: project_permission_config.name,
        children: []
      }
      hash[:children] = project_permission_config.child_project_permission_configs.where(organization_id: current_organization_id).map do |child_project_permission_config|
        selected = ProjectRolePermission.exists?(project_permission_config_id: child_project_permission_config.id, project_role_config_id: @project_role_config.id, organization_id: current_organization_id)
        {
          name: child_project_permission_config.name,
          value: child_project_permission_config.id,
          selected: selected
        }
      end
      @select_data << hash
    end

  end

  # POST /admin/project_role_configs
  def create
    @project_role_config = ProjectRoleConfig.new(project_role_config_params)
    select_ids = params[:select].split(',')
    select_ids.each do |select_id|
      @project_role_config.project_role_permissions.build(project_permission_config_id: select_id, organization_id: current_organization_id)
    end
    @status = @project_role_config.save
  end

  # PATCH/PUT /admin/project_role_configs/1
  def update
    begin
      ActiveRecord::Base.transaction do
        @status = true
        select_ids = params[:select].split(',')
        @project_role_config.project_role_permissions.where.not(project_permission_config_id: select_ids).destroy_all
        select_ids.each do |select_id|
          @project_role_config.project_role_permissions.build(project_permission_config_id: select_id, organization_id: current_organization_id) unless ProjectRolePermission.exists?(project_permission_config_id: select_id, project_role_config_id: @project_role_config.id, organization_id: current_organization_id)
        end
        @project_role_config.update!(project_role_config_params)
      end
    rescue => exception
      @status = false
      Rails.logger.info exception.message
      @msg = "保存失败了"
    end
  end

  def update_is_default
    @status = @project_role_config.update(is_default: true)
    if @status
      ProjectRoleConfig.where(organization_id: current_organization_id)
        .where.not(id: @project_role_config.id).update_all(is_default: false)
    end
  end

  # DELETE /admin/project_role_configs/1
  def destroy
    @status = @project_role_config.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_project_role_config
      @project_role_config = ProjectRoleConfig.find_by(id: params[:id], organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def project_role_config_params
      params.require(:project_role_config).permit(:is_default, :name, :organization_id)
    end
end
