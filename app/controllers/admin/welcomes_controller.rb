class Admin::Welcomes<PERSON>ontroller < Admin::ApplicationController
  before_action do
    authorize "Welcome", policy_class: WelcomePolicy
  end

  # GET /admin/welcomes
  def index
    # 待审核
    @approval_step_user_count = current_user.approval_step_users.where(organization_id: current_organization_id).in_progress.count

    # # 查询用户担任的项目角色是FAE的项目ID
    # project_ids = current_user.project_users.joins(project_role_config: :project_permission_configs).where(project_permission_configs: {key: 'FAE'}).distinct.pluck(:project_id)
    # # 待指派
    # @wait_assign_count = WorkOrder.includes(:project).where(project_id: project_ids, aasm_state: 'not_started', projects: {status: 'progressing'}).count
  end

  # 本周任务
  def current_week_data
    @started_at = Time.now.beginning_of_week
    @ended_at = Time.now.end_of_week
    tasks
  end

  # 下周任务
  def next_week_data
    @started_at = (Time.now + 1.week).beginning_of_week
    @ended_at = (Time.now + 1.week).end_of_week
    tasks
  end

  def approval_step_users_list
    if params[:tag].blank?
      if params[:status].blank?
        approval_step_users = current_user.approval_step_users.where(organization_id: current_organization_id).in_progress
      else
        approval_step_users = current_user.approval_step_users.where(organization_id: current_organization_id).where(status: params[:status])
      end

      result = approval_step_users.includes(approval_step: :approval_flow).page(params[:page]).per(params[:limit]).map do |approval_step_user|
        source_id, source_title = approval_step_user.approval_step.approval_flow.source_title_id
        {
          id: approval_step_user.id,
          name: approval_step_user.approval_step.name,
          source_type: approval_step_user.approval_step.approval_flow.source_type,
          source_id: source_id,
          source_title: source_title,
          status: approval_step_user.status_i18n
          # button: approval_step_user.get_button
        }
      end
      render json: {code: 0, msg: "", count: approval_step_users.count, data: result}
    end
  end

  # 更新审核记录
  def update_approval_step_user
    begin
      ActiveRecord::Base.transaction do
        approval_step_user = current_user.approval_step_users.find_by(id: params[:id])
        case params[:status]
        when 'approved', 'rejected'
          approval_step_user.update(status: params[:status], review_comment: params[:review_comment], operating_at: Time.now)
        end
      end
      render json: {status: true}
    rescue => exception
      Rails.logger.error("更新审核步骤用户失败: #{exception.message}")
      render json: {status: false, message: '更新审核步骤用户失败'}
    end

  end

  # 更新计划状态
  def update_plan_status
    begin
      @status = true
      time_now = Time.now
      ActiveRecord::Base.transaction do
        @plan = current_user.duty_plans.find_by(id: params[:id])
        case params[:status]
        when 'in_progress'
          @plan.update!(status: params[:status], act_started_at: time_now)
          @message = "任务已开始！"
        when 'discontinued'
          @plan.update!(status: params[:status])
          @message = "任务已中止！如需恢复请联系项目经理"
        when 'completed'
          @plan.update!(status: params[:status], act_ended_at: time_now)
          @message = "任务已完成！"
        else
          @message = "操作失败, 未知状态"
        end
      end
      # render json: {status: true, message: @message}
    rescue => exception
      Rails.logger.error("更新计划状态失败: #{exception.message}")
      @message = "更新计划状态失败, #{exception.message}"
      @status = false
      # render json: {status: false, message: '更新计划状态失败'}
    end
  end

  # 更新工单状态
  def update_work_order_status
    begin
      @status = true
      time_now = Time.now
      ActiveRecord::Base.transaction do
        @work_order = WorkOrder.where_current_user(current_user).find_by(id: params[:id])
        case params[:status]
        when 'confirm_closed', 'manual_closed', 'already_solved'
          @work_order.closed!
          @work_order.update!(customer_confirmation: params[:reason], reason_type_for_log: params[:status], closed_at: time_now)
          @message = "任务已关闭！"
          #  act_ended_at                                   :datetime
        when 'appoint' # 指派责任人
          @work_order.in_progress!
          @work_order.update!(obligation_user_id: params[:obligation_user_id], act_started_at: time_now)
          @message = "任务指派成功！"
        when 'unacceptance' # 拒绝
          @work_order.closed!
          @work_order.update!(rejection_reason: params[:rejection_reason], closed_at: time_now)
          @message = "任务已驳回！"
        when 'solved' # 解决
          @work_order.solved!
          @work_order.update!(solution: params[:solution], act_ended_at: time_now)
          @message = "任务已解决！"
        when 'add_receiver_user' # 指派评估人"
          @work_order.evaluate!
          @work_order.update!(receiver_user_id: params[:receiver_user_id])
          @message = "已指派评估人"
        when 'unsolved' # 不受理此任务
          @work_order.review_failed!
          @work_order.update!(customer_confirmation: params[:customer_confirmation])
          self.update_columns(act_ended_at: nil)
          @message = "任务已退回"
        else
          @message = "操作失败, 未知状态"
        end
      end
      # render json: {status: true, message: @message}
    rescue => exception
      Rails.logger.error("更新计划状态失败: #{exception.message}")
      @message = "更新计划状态失败"
      @status = false
      # render json: {status: false, message: '更新计划状态失败'}
    end
  end

  # 更新风险状态
  def update_project_risk_status
    begin
      @status = true
      time_now = Time.now
      ActiveRecord::Base.transaction do
        @project_risk = current_user.project_risks.find_by(id: params[:id])
        case params[:status]
        when 'closed'
          @project_risk.update!(close_reason: params[:reason], aasm_state: 'closed', act_ended_at: time_now)
          @message = "任务已关闭！"
        when 'appoint' # 指派责任人
          @project_risk.update!(obligation_user_id: params[:obligation_user_id], aasm_state: 'tracked', act_started_at: time_now)
          @message = "任务指派成功！"
        else
          @message = "操作失败, 未知状态"
        end
      end
    rescue => exception
      Rails.logger.error("更新计划状态失败: #{exception.message}")
      @message = "更新计划状态失败"
      @status = false
    end
  end

  # @status = @work_order.update(work_order_params)
  # case @work_order.aasm_state
  # when 'not_started'
  #   @work_order.evaluate! if @work_order.receiver_user_id.present? && button_tag == "receiver" #点击的指定评估人按钮
  # when 'evaluating'
  #   @work_order.in_progress! if @work_order.obligation_user_id.present? && button_tag == "appoint" #受理，指定责任人
  #   @work_order.closed! if button_tag == "unacceptance" #不受理，驳回
  # when 'in_progress'
  #   @work_order.solved! if button_tag == "solved" #责任人解决
  # when 'solved'
  #   @work_order.in_progress! if button_tag == "unsolved" #未解决
  # when 'close'
  #   @work_order.reopened!
  # end



  # 近期参与项目
  def projects
    projects = Project.left_joins(:project_users).where(status: 'progressing').where("projects.user_id = ? OR project_users.user_id = ?", current_user.id, current_user.id).distinct.limit(5)
    result = projects.includes(:project_organizations).map do |project|
      {
        id: project.id,
        name: project.name,
        project_type: project.project_type_i18n,
        organization_name: project.organization.name
      }
    end
    render json: {code: 0, msg: "", count: projects.count, data: result}
  end

  private

  def tasks
    # 时间段范围内的任务
    tasks = current_user.duty_plans.includes(:project).where(projects: {status: 'progressing'})
      .where("(started_at BETWEEN :start AND :end) OR (ended_at BETWEEN :start AND :end) OR (plans.status IN (:statuses) AND ended_at < :time_now)", start: @started_at, end: @ended_at, statuses: [Plan.statuses['no_start'], Plan.statuses['in_progress']], time_now: Time.now)
      .order(:ended_at)

    tasks += WorkOrder.where_current_user(current_user).includes(:project).where(projects: {status: 'progressing'}, aasm_state: ["in_progress", "evaluating", "reopen"])
      .where("(started_at BETWEEN :start AND :end) OR (ended_at BETWEEN :start AND :end)", start: @started_at, end: @ended_at)
      .order(:ended_at)

    # 查询用户担任的项目角色是FAE的项目ID
    project_ids = current_user.project_users.joins(project_role_config: :project_permission_configs).where(project_permission_configs: {key: 'FAE'}).distinct.pluck(:project_id)
    # 待指派
    tasks += WorkOrder.includes(:project).where(project_id: project_ids, aasm_state: 'not_started', projects: {status: 'progressing'})
    # 风险
    tasks += current_user.duty_project_risks.where(project_id: project_ids, aasm_state: 'tracked').where("(started_at BETWEEN :start AND :end) OR (ended_at BETWEEN :start AND :end)", start: @started_at, end: @ended_at).order(:ended_at)
    tasks += current_user.project_risks.where(project_id: project_ids, aasm_state: 'identified').order(:ended_at)

    result = tasks.map do |task|
      data_type = task.class.to_s
      name = data_type == 'WorkOrder' ? task.title : task.name
      {
        id: task.id,
        name: name,
        project_name: task.project.name,
        project_id: task.project_id,
        status: task.status_i18n,
        around_time: "#{task.started_at.strftime("%F %T")} ~ #{task.ended_at.strftime("%F %T")}",
        data_type: data_type,
        data_type_title: task.data_type_title,
        url: get_url(task),
        tab: data_type.underscore.pluralize,
        priority: task.priority_color,
        button: task.get_button(current_user)
      }
    end
    render json: {code: 0, msg: "", count: tasks.count, data: result}
  end

  def get_url(task)
    case task.class.to_s
    when 'WorkOrder'
      "/admin/work_orders/#{task.id}"
    when 'Plan'
      "/admin/plans/#{task.id}"
    when 'ProjectRisk'
      "/admin/project_risks/#{task.id}"
    end
  end

end
