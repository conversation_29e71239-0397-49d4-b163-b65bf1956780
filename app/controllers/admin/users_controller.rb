class Admin::UsersController < Admin::ApplicationController
  before_action do
    authorize User
  end

  before_action :set_user, only: [:show, :edit, :update, :destroy]

  # GET /admin/users
  def index
    @users = current_user.organization.users.order(created_at: :desc)
    if request.xhr?
      result = @users.page(params[:page]).per(params[:limit]).map do |user|
        {
          id: user.id,
          name: user.name,
          avatar: user.avatar_url,
          phone: user.phone,
          recommend_user_name: user.recommend_user&.name,
          created_at: user.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
      end
      render json: {code: 0, msg: "", count: @users.count, data: result}
    end
  end

  # GET /admin/users/1
  def show
  end

  # GET /admin/users/new
  def new
    @user = current_user.organization.users.new
  end

  # GET /admin/users/1/edit
  def edit
  end

  # POST /admin/users
  def create
    begin
      @user = current_user.organization.users.new(user_params.merge(status: 'active'))
      @user.save!
      @user.roles.destroy_all
      params[:select]&.split(',')&.each do |role_id|
        @user.roles << current_user.organization.roles.find(role_id)
      end
      @status = true
    rescue => exception
      @status = false
      @msg = "失败了, #{exception.message}"
    end
  end

  # PATCH/PUT /admin/users/1
  def update
    begin
      @user.update!(user_params)
      @user.roles.destroy_all
      params[:select]&.split(',')&.each do |role_id|
        @user.roles << current_user.organization.roles.find(role_id)
      end
      @status = true
    rescue => exception
      @status = false
      @msg = "失败了, #{exception.message}"
    end
  end

  # DELETE /admin/users/1
  def destroy
    @status = @user.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_user
      @user = User.find(params[:id])
    end

    # Only allow a trusted parameter "white list" through.
    def user_params
      params.require(:user).permit(:name, :phone, :password, :password_confirmation, :avatar)
    end
end
