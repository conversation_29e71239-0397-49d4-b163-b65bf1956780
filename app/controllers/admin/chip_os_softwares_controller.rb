class Admin::ChipOsSoftwaresController < Admin::ApplicationController
  before_action do
    authorize ChipOsSoftware
  end

  before_action :set_chip_os_software, only: [:show, :edit, :update, :destroy, :get_version]

  # GET /admin/chip_os_softwares
  def index
    @chip_os_softwares = ChipOsSoftware.where(organization_id: current_organization_id).order(created_at: :desc)
    if request.xhr?
      @result = @chip_os_softwares.page(params[:page]).per(params[:limit]).map do |chip_os_software|
        {
          id: chip_os_software.id,
          name: chip_os_software.name,
          chip_config_name: chip_os_software.chip_config&.name,
          version: chip_os_software.default_version
        }
      end
      render json: {code: 0, data: @result, count: @chip_os_softwares.count}
    end
  end

  # GET /admin/chip_os_softwares/1
  def show
  end

  # GET /admin/chip_os_softwares/new
  def new
    @chip_config_array = ChipConfig.where(organization_id: current_organization_id, status: true).pluck(:name, :id)
    @chip_os_software = ChipOsSoftware.new(organization_id: current_organization_id)
  end

  # GET /admin/chip_os_softwares/1/edit
  def edit
    @chip_config_array = ChipConfig.where(organization_id: current_organization_id, status: true).pluck(:name, :id)
  end

  # POST /admin/chip_os_softwares
  def create
    @chip_os_software = ChipOsSoftware.new(chip_os_software_params.merge(organization_id: current_organization_id))
    @status = @chip_os_software.save
  end

  # PATCH/PUT /admin/chip_os_softwares/1
  def update
    @status = @chip_os_software.update(chip_os_software_params)
  end

  # DELETE /admin/chip_os_softwares/1
  def destroy
    @status = @chip_os_software.destroy
  end

  def get_version
    chip_os_versions = @chip_os_software.chip_os_versions.order(is_default: :desc, created_at: :desc)
    render json: {status: true, data: chip_os_versions.map { |chip_os_version| chip_os_version.attributes.slice('id', 'version', 'is_default') }}
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_chip_os_software
      @chip_os_software = ChipOsSoftware.find_by(id: params[:id], organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def chip_os_software_params
      params.require(:chip_os_software).permit(:name, :organization_id, :chip_config_id)
    end
end
