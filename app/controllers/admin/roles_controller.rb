class Admin::RolesController < Admin::ApplicationController
  before_action :set_role, only: [:show, :edit, :update, :destroy]
  before_action do
    authorize Role
  end

  # GET /admin/roles
  def index
    @q = current_user.organization.roles.order(created_at: :desc).ransack(params[:q])
    @roles = @q.result
    if request.xhr?
      @result = @roles.page(params[:page]).per(params[:limit]).map do |role|
        {
          id: role.id,
          name: role.name,
          description: role.description,
          organization_id: role.organization_id
        }
      end
      render json: {code: 0, data: @result, count: @roles.count}
    end
  end

  # GET /admin/roles/1
  def show
  end

  # GET /admin/roles/new
  def new
    @role = current_user.organization.roles.new
  end

  # GET /admin/roles/1/edit
  def edit
  end

  # POST /admin/roles
  def create
    @role = current_user.organization.roles.new(role_params)

    if @role.save
      if params[:role][:permission_action_ids].present?
        @role.role_permissions.destroy_all

        params[:role][:permission_action_ids].reject { |s| s.empty? }&.each do |permission_action_id|
          permission_action = current_user.organization.permission_actions.find(permission_action_id)
          @role.role_permissions.create!(
            permission_action_id: permission_action.id,
            organization_id: current_user.organization_id
          )
        end
      end
      @status = true
    else
      @status = false
      @msg = "保存失败了, #{@role.errors.full_messages.to_sentence}"
    end
  end

  # PATCH/PUT /admin/roles/1
  def update
    if @role.update(role_params)
      if params[:role][:permission_action_ids].present?
        @role.role_permissions.destroy_all

        params[:role][:permission_action_ids].reject { |s| s.empty? }&.each do |permission_action_id|
          permission_action = current_user.organization.permission_actions.find(permission_action_id)
          @role.role_permissions.create!(
            permission_action_id: permission_action.id,
            organization_id: current_user.organization_id
          )
        end
      end
      @status = true
    else
      @status = false
      @msg = "保存失败了, #{@role.errors.full_messages.to_sentence}"
    end
  end

  # DELETE /admin/roles/1
  def destroy
    if @role.destroy
      @status = true
    else
      @status = false
      @msg = "删除失败了, #{@role.errors.full_messages.to_sentence}"
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_role
      @role = current_user.organization.roles.find(params[:id])
    end

    # Only allow a trusted parameter "white list" through.
    def role_params
      params.require(:role).permit(:name, :description, :organization_id)
    end
end
