class Admin::ProjectRisksController < Admin::ApplicationController
  before_action do
    authorize ProjectRisk
  end
  before_action :set_project
  before_action :set_project_risk, only: [:show, :edit, :update, :destroy, :change_state]

  # GET /admin/project_risks
  def index
    if request.xhr?
      @q = ProjectRisk.order(created_at: :desc).ransack(params[:q])
      @project_risks = @q.result.page(params[:page]).per(params[:limit])
      @result = @project_risks.map do |project_risk|
        risk_hash = project_risk.as_json(only: [:id, :name, :which_module, :description, :coping_strategy, :action_plan, :close_reason])
        risk_hash.merge({
          started_at: project_risk.started_at&.strftime("%Y-%m-%d %H:%M:%S"),
          ended_at: project_risk.ended_at&.strftime("%Y-%m-%d %H:%M:%S"),
          act_started_at: project_risk.act_started_at&.strftime("%Y-%m-%d %H:%M:%S"),
          act_ended_at: project_risk.act_ended_at&.strftime("%Y-%m-%d %H:%M:%S"),
          created_at: project_risk.created_at.strftime("%Y-%m-%d %H:%M:%S"),
          updated_at: project_risk.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
          # display_button: project_risk.is_display_button(current_user),
          aasm_state: project_risk.aasm_state_i18n,
          problem_severity: project_risk.problem_severity_i18n,
          risk_type: project_risk.risk_type_i18n,
          risk_level: project_risk.priority_color,
          nature: project_risk.nature_i18n,
          status: project_risk.aasm_state,
          tab: project_risk.class.to_s.underscore.pluralize,
          button: project_risk.get_button(current_user),
          around_time: "#{project_risk.started_at.strftime("%F %T")} ~ #{project_risk.ended_at.strftime("%F %T")}",
          project_id: project_risk.project_id
        })
      end
      render json: {code: 0, msg: 'success', data: @result, count: @project_risks.count}
    end
  end

  # GET /admin/project_risks/1
  def show
    @comment_tree = @project_risk.comments_json
    @commentable_type = 'ProjectRisk'
    @commentable_id = @project_risk.id
  end

  # GET /admin/project_risks/new
  def new
    @project_risk = ProjectRisk.new
  end

  # GET /admin/project_risks/1/edit
  def edit
    @tag = params[:tag]
  end

  # POST /admin/project_risks
  def create
    @project_risk = ProjectRisk.new(project_risk_params.merge(aasm_state: 'identified', user_id: current_user.id))
    @status = @project_risk.save
  end

  # PATCH/PUT /admin/project_risks/1
  def update
    button_tag = project_risk_params[:button_tag]
    @status = @project_risk.update(project_risk_params)
    case @project_risk.aasm_state
    when 'identified'
      if @project_risk.obligation_user_id.present? && button_tag == "appoint" #指定责任人
        @project_risk.tracked!
      end
    end
  end

  # DELETE /admin/project_risks/1
  def destroy
    @status = @project_risk.destroy
  end

  #改变状态
  def change_state
    case params[:tag]
    when "closed"
      @project_risk.closed!
      @project_risk.update(project_risk_params)
    end
    @status = true
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_project_risk
      @project_risk = ProjectRisk.find(params[:id])
    end

    def set_project
      @project = current_organization.projects.find_by(id: params[:project_id])
    end

    # Only allow a trusted parameter "white list" through.
    def project_risk_params
      params.require(:project_risk).permit(:project_id, :button_tag, :obligation_user_id, :riskable_id, :riskable_type, :name, :risk_type, :which_module, :aasm_state, :description, :risk_level, :nature, :problem_severity, :coping_strategy, :action_plan, :close_reason, :started_at, :ended_at, :act_started_at, :act_ended_at)
    end
end
