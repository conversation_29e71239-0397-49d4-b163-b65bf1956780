class Admin::ProductCategoriesController < Admin::ApplicationController
  before_action do
    authorize ProductCategory
  end

  before_action :set_product_category, only: [:show, :edit, :update, :destroy]

  # GET /admin/product_categories
  def index
    @product_categories = ProductCategory.where(organization_id: current_organization_id).order(created_at: :desc)
    if request.xhr?
      @result = @product_categories.page(params[:page]).per(params[:limit]).map do |product_category|
        {
          id: product_category.id,
          name: product_category.name,
          created_at: product_category.created_at.strftime('%F %T')
        }
      end
      render json: {code: 0, data: @result, count: @product_categories.count}
    end
  end

  # GET /admin/product_categories/1
  def show
  end

  # GET /admin/product_categories/new
  def new
    @product_category = ProductCategory.new(organization_id: current_organization_id)
  end

  # GET /admin/product_categories/1/edit
  def edit
  end

  # POST /admin/product_categories
  def create
    @product_category = ProductCategory.new(product_category_params.merge(organization_id: current_organization_id))
    @status = @product_category.save
  end

  # PATCH/PUT /admin/product_categories/1
  def update
    @status = @product_category.update(product_category_params)
  end

  # DELETE /admin/product_categories/1
  def destroy
    @status = @product_category.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_product_category
      @product_category = ProductCategory.find_by(id: params[:id],organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def product_category_params
      params.require(:product_category).permit(:name, :organization_id)
    end
end
