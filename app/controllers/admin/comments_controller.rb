class Admin::CommentsController < Admin::ApplicationController
  before_action do
    authorize Comment
  end

  def create
    @comment = Comment.new(comment_params)
    @comment.user = current_user
    @comment.organization_id = current_organization_id
    @commentable_type = params[:comment][:commentable_type]
    @commentable_id = params[:comment][:commentable_id]

    @status = @comment.save
    @comment_tree = @comment.commentable.comments_json

  end

  # def update
  #   @comment = Comment.find(params[:id])
  #   @status = @comment.update(comment_params)
  # end

  def destroy
    @comment = Comment.find(params[:id])
    @status = @comment.destroy
    @commentable_type = params[:commentable_type]
    @commentable_id = params[:commentable_id]
    @comment_tree = @comment.commentable.comments_json
  end

  private

  def comment_params
    params.require(:comment).permit(:content, :parent_id, :commentable_id, :commentable_type)
  end

end