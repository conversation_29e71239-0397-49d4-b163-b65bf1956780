class Admin::ProjectOrganizationsController < Admin::ApplicationController
  before_action :set_project_organization, only: [:show, :edit, :update, :destroy]
  before_action :set_project

  before_action do
    authorize ProjectOrganization
  end

  # GET /admin/project_organizations
  def index

    if request.xhr?
      @project_organizations = @project.project_organizations.order(p_type: :asc, created_at: :desc)
      @result = @project_organizations.includes(:organization).page(params[:page]).per(params[:limit]).map do |project_organization|
        organization = project_organization.organization
        undeleted_status = project_organization.owner? || @project.main_project_organization.organization.parent_id == organization.id
        {
          id: project_organization.id,
          name: organization.name,
          status: project_organization.status_i18n,
          operated_at: project_organization.operated_at&.strftime('%F %T'),
          p_type: project_organization.p_type_i18n,
          undeleted_status: undeleted_status
        }
      end
      render json: {code: 0, msg: 'success', data: @result}
    end
  end

  # GET /admin/project_organizations/1
  def show
  end

  # GET /admin/project_organizations/new
  def new
    @project_organization = ProjectOrganization.new(p_type: 'member', status: 'opened', project_id: params[:project_id])
  end

  # GET /admin/project_organizations/1/edit
  def edit
  end

  # POST /admin/project_organizations
  def create
    @project_organization = ProjectOrganization.new(project_organization_params)
    @status = @project_organization.save
  end

  # PATCH/PUT /admin/project_organizations/1
  def update
    @status = @project_organization.update(project_organization_params)
  end

  # DELETE /admin/project_organizations/1
  def destroy
    @status = @project_organization.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_project_organization
      @project_organization = ProjectOrganization.find(params[:id])
    end

    def set_project
      @project = Project.find_by(id: params[:project_id])
    end

    # Only allow a trusted parameter "white list" through.
    def project_organization_params
      params.require(:project_organization).permit(:p_type, :operated_at, :status, :organization_id, :project_id)
    end
end
