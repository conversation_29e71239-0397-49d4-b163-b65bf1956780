class Admin::UserNoticesController < Admin::ApplicationController

  before_action do
    authorize UserNotice
  end

  def index
    if request.xhr?
      user_notices = current_user.user_notices.order(:status, created_at: :desc)
      result = user_notices.page(params[:page]).per(params[:limit]).map do |user_notice|
        {
          id: user_notice.id,
          name: user_notice.name,
          content: user_notice.content,
          url: user_notice.url,
          status: user_notice.status,
          created_at: user_notice.created_at.strftime('%F %T'),
          url_open_way: user_notice.url_open_way
        }
      end
      render json: {code: 0, msg: 'success', data: result}
    end

  end

  def update
    ids = params[:id].to_s.split(',')
    current_user.user_notices.where(id: ids).update_all(status: true)
  end

end
