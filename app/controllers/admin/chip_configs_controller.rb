class Admin::ChipConfigsController < Admin::ApplicationController
  before_action do
    authorize ChipConfig
  end

  before_action :set_chip_config, only: [:show, :edit, :update, :destroy]

  # GET /admin/chip_configs
  def index
    @chip_configs = ChipConfig.where(organization_id: current_organization_id).order(created_at: :desc)
    if request.xhr?
      @result = @chip_configs.page(params[:page]).per(params[:limit]).map do |chip_config|
        {
          id: chip_config.id,
          name: chip_config.name,
          c_type: chip_config.c_type_i18n,
          code: chip_config.code,
          product_line: chip_config.product_line_i18n,
          status: chip_config.status ? '启用' : '禁用',
          description: chip_config.description,
          product_category_id: chip_config.product_category&.name
        }
      end
      render json: {code: 0, data: @result, count: @chip_configs.count}
    end
  end

  # GET /admin/chip_configs/1
  def show
  end

  # GET /admin/chip_configs/new
  def new
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
    @chip_config = ChipConfig.new(organization_id: current_organization_id)
  end

  # GET /admin/chip_configs/1/edit
  def edit
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
  end

  # POST /admin/chip_configs
  def create
    @chip_config = ChipConfig.new(chip_config_params.merge(organization_id: current_organization_id))
    @status = @chip_config.save!
  end

  # PATCH/PUT /admin/chip_configs/1
  def update
    @status = @chip_config.update(chip_config_params)
  end

  # DELETE /admin/chip_configs/1
  def destroy
    @status = @chip_config.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_chip_config
      @chip_config = ChipConfig.find_by(id: params[:id], organization_id: current_organization_id)
    end

    # Only allow a trusted parameter "white list" through.
    def chip_config_params
      params.require(:chip_config).permit(:name, :organization_id, :c_type, :code, :product_line, :status, :description, :product_category_id)
    end
end
