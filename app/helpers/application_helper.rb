module ApplicationHelper
  def crumbs_list(actions, url='/')
    content_tag(:span, class: 'layui-breadcrumb', style: 'padding: 10px') do
      actions.each do |action|
        url = "#{url}#{action}/"
        name = action.to_i > 0 ? action : (I18n.t action, scope: [:actions])
        if actions.last == action
          cite = content_tag(:cite, (name))
          concat content_tag(:a, cite, href: url)
        else
          concat content_tag(:a, (name), href: url)
        end
      end
    end
  end

  def humanized_time(time)
    return "刚刚" if time > 5.minutes.ago
    return "#{(Time.now - time).to_i / 60}分钟前" if time > 1.hour.ago
    return "1小时前" if time > 90.minutes.ago
    return "#{(Time.now - time).to_i / 3600}小时前" if time > 1.day.ago
    time.strftime("%Y-%m-%d %H:%M")
  end

end
