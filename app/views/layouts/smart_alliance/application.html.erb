<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>项目管理系统</title>
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag 'admin/application', media: 'all' %>
  <%= javascript_include_tag 'admin/application', media: 'all' %>
</head>

<body class="layui-layout-body" id="LAY_home_iframe">
  <div id="LAY_app" style="visibility: hidden">
    <div class="layui-layout layui-layout-admin">
      <%= render "layouts/admin/header"%>
      <!-- <div class="layui-header">
        <div class="layui-logo layui-hide-xs layui-bg-black">AI服务系统</div>
        <ul class="layui-nav layui-layout-left" style="line-height: 60px;">
          <% if request.fullpath != '/admin' %>
            <% actions = request.fullpath.split('/')  %>
            <% actions.delete('') %>
            <%= crumbs_list(actions) %>
          <% end %>
        </ul>
      </div> -->
      <%= render "layouts/admin/left_nav"%>
      <%= render "layouts/admin/pagetabs"%>

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="<%= admin_welcomes_path %>" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>

      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
    <%#= render "layouts/admin/notice" %>
    <%#= yield :script %>

  </div>
  <script>
    layui.config({
        base: '/assets/smart_alliance/' // 静态资源所在路径
    }).use(['index']);

    $(document).on('click', '#change-password', function(){
      layer.open({
        type: 1,
        area: '350px',
        resize: false,
        shade: 0.8,
        shadeClose: false,
        title: "修改密码",
        content: `
          <div class="layui-form" lay-filter="filter-test-layer" style="margin: 16px;">
            <div class="demo-login-container">
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="password" value="" lay-verify="required" placeholder="旧密码" lay-reqtext="旧密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="new_password" value="" lay-verify="required" placeholder="新密码" lay-reqtext="请填写新密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="new_password_confirmation" value="" lay-verify="required" placeholder="确认密码" lay-reqtext="请再次填写密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>

              <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="file_user_set_update_password">保存</button>
              </div>

            </div>
          </div>
        `,
        success: function(){
          // 对弹层中的表单进行初始化渲染
          layui.form.render();
          // 表单提交事件
          layui.form.on('submit(file_user_set_update_password)', function(data){
            var field = data.field; // 获取表单字段值
            $.ajax({
              type: "POST",
              url: '/admin/sessions/update_password',
              data: field,
              success:function(res){
                if (res.status){
                  window.location.href = '/admin/sessions'
                }else{
                  layer.msg(res.msg)
                }
              }
            })
            return false; // 阻止默认 form 跳转
          });
        }
      });
    })
  </script>

</body>
</html>

