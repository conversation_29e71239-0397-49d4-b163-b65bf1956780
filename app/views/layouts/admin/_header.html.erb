<div class="layui-header">
  <!-- 头部区域 -->
  <ul class="layui-nav layui-layout-left">
    <li class="layui-nav-item layadmin-flexible" lay-unselect>
      <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
        <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
      </a>
    </li>
    <!-- <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <a href="" target="_blank" title="前台">
        <i class="layui-icon layui-icon-website"></i>
      </a>
    </li> -->
    <li class="layui-nav-item" lay-unselect>
      <a href="javascript:;" layadmin-event="refresh" title="刷新">
        <i class="layui-icon layui-icon-refresh-3"></i>
      </a>
    </li>
    <!-- <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <input type="text" placeholder="搜索..." autocomplete="off" class="layui-input layui-input-search"
        layadmin-event="serach" lay-action="/admin/welcomes/search?keywords=">
    </li> -->
  </ul>
  <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">

    <li class="layui-nav-item" lay-unselect>

      <a lay-href="/admin/user_notices" layadmin-event="message" lay-text="消息中心">
        <i class="layui-icon layui-icon-notice"></i>

        <!-- 如果有新消息，则显示小圆点 -->
        <% if current_user.user_notices.where(status: false).count > 0 %>
          <span class="layui-badge notice_count"><%= current_user.user_notices.where(status: false).count %></span>
        <% end %>
      </a>

    </li>
    <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <a href="javascript:;" layadmin-event="note">
        <i class="layui-icon layui-icon-note"></i>
      </a>
    </li>
    <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <a href="javascript:;" layadmin-event="theme">
        <i class="layui-icon layui-icon-theme"></i>
      </a>
    </li>
    <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <a href="javascript:;" layadmin-event="fullscreen">
        <i class="layui-icon layui-icon-screen-full"></i>
      </a>
    </li>
    <li class="layui-nav-item" lay-unselect>
      <a href="javascript:;">
        <cite><%= current_user&.name %></cite>
      </a>
      <dl class="layui-nav-child">
        <!-- <dd><a lay-href="set/user/info.html">基本资料</a></dd>
        <dd><a lay-href="set/user/password.html">修改密码</a></dd> -->
        <dd><a href="javascript:;" id="change-password">修改密码</a></dd>
        <hr>
        <!-- <dd layadmin-event="logout" style="text-align: center;"><a>退出</a></dd> -->
        <dd><a href="<%= logout_admin_sessions_path %>" data-method="post">注销</a></dd>
      </dl>
    </li>

    <li class="layui-nav-item layui-hide-xs" lay-unselect>
      <a href="javascript:;" layadmin-event="about"><i class="layui-icon layui-icon-more-vertical"></i></a>
    </li>
    <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-unselect>
      <a href="javascript:;" layadmin-event="more"><i class="layui-icon layui-icon-more-vertical"></i></a>
    </li>
  </ul>
</div>