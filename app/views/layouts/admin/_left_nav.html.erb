<div class="layui-side layui-side-menu">
  <div class="layui-side-scroll">
    <div class="layui-logo" lay-href="<%= admin_welcomes_path %>">
      <span><%= current_organization.name %>-项目管理</span>
    </div>

    <ul class="layui-nav layui-nav-tree" lay-accordion id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
      <li data-name="home" class="layui-nav-item layui-nav-itemed">
        <a href="javascript:;" lay-tips="主页" lay-direction="2">
          <i class="layui-icon layui-icon-home"></i>
          <cite>主页</cite>
        </a>
        <dl class="layui-nav-child">
          <dd data-name="home1" class="layui-this">
            <a lay-href="<%= admin_welcomes_path %>">主页</a>
          </dd>
        </dl>
      </li>

      <% if policy(:project).index? %>
        <li data-name="projects" class="layui-nav-item">
          <a href="javascript:;" lay-tips="项目" lay-direction="2">
            <i class="layui-icon layui-icon-component"></i>
            <cite>项目</cite>
          </a>
          <dl class="layui-nav-child">
            <dd data-name="projects">
              <a lay-href="<%= admin_projects_path %>">项目列表</a>
            </dd>
          </dl>
          <dl class="layui-nav-child">
            <dd data-name="projects">
              <a lay-href="<%= admin_admin_projects_path %>">全部项目</a>
            </dd>
          </dl>
        </li>
      <% end %>

      <!--工单-->
      <% if policy(:work_order).index? %>
        <li data-name="work_orders" class="layui-nav-item">
          <a href="javascript:;" lay-tips="工单" lay-direction="2">
            <i class="layui-icon layui-icon-set"></i>
            <cite>工单</cite>
          </a>
          <dl class="layui-nav-child">
            <dd data-name="work_order_list">
              <a lay-href="<%= admin_work_orders_path %>">我的工单</a>
            </dd>
          </dl>
        </li>
      <% end %>

      <% if policy(:user).index? %>
        <li data-name="user" class="layui-nav-item">
          <a href="javascript:;" lay-tips="用户" lay-direction="2">
            <i class="layui-icon layui-icon-user"></i>
            <cite>用户</cite>
          </a>
          <dl class="layui-nav-child">
            <dd>
              <a data-name="user_lists" lay-href="<%= admin_users_path %>">列表</a>
            </dd>
          </dl>
        </li>
      <% end %>
      <% if current_user.is_admin? && (Settings.main_organization_id == current_user.organization_id) %>
        <li data-name="organization" class="layui-nav-item">
          <a href="javascript:;" lay-tips="组织" lay-direction="2">
            <i class="layui-icon layui-icon-group"></i>
            <cite>组织</cite>
          </a>
          <dl class="layui-nav-child">
            <dd>
              <a data-name="organization_lists" lay-href="<%= admin_organizations_path %>">列表</a>
            </dd>
          </dl>
        </li>
      <% end %>
      <% if policy(:role).index? %>
        <li data-name="role" class="layui-nav-item">
          <a href="javascript:;" lay-tips="权限" lay-direction="2">
            <i class="layui-icon layui-icon-group"></i>
            <cite>角色与权限</cite>
          </a>
          <dl class="layui-nav-child">
            <dd>
              <a data-name="role_lists" lay-href="<%= admin_roles_path %>">列表</a>
            </dd>
          </dl>
        </li>
      <% end %>

      <%# 系统配置权限控制 %>
      <% if policy(:organization_flow).index? || policy(:project_role_config).index? || policy(:product_category).index? || policy(:chip_config).index? || policy(:chip_os_software).index? %>
        <li data-name="system_config" class="layui-nav-item">
          <a href="javascript:;" lay-tips="系统配置" lay-direction="2">
            <i class="layui-icon layui-icon-set-sm"></i>
            <cite>系统配置</cite>
          </a>
          <dl class="layui-nav-child">

            <% if policy(:organization_flow).index? %>
              <dd>
                <a data-name="organization_flow_lists" lay-href="<%= admin_organization_flows_path %>">审核流程配置</a>
              </dd>
            <% end %>

            <% if policy(:project_role_config).index? %>
              <dd>
                <a data-name="project_role_config_lists" lay-href="<%= admin_project_role_configs_path %>">项目角色权限</a>
              </dd>
            <% end %>

            <% if policy(:product_category).index? %>
              <dd>
                <a data-name="product_category_lists" lay-href="<%= admin_product_categories_path %>">产品分类</a>
              </dd>
            <% end %>

            <% if policy(:chip_config).index? || policy(:chip_os_software).index? %>
              <dd data-name="assistants">
                <a href="javascript:;">芯片平台</a>
                <dl class="layui-nav-child">
                  <% if policy(:chip_config).index? %>
                    <dd>
                      <a data-name="chip_config_lists" lay-href="<%= admin_chip_configs_path %>">芯片配置</a>
                    </dd>
                  <% end %>
                  <% if policy(:chip_os_software).index? %>
                    <dd>
                      <a data-name="chip_os_software_lists" lay-href="<%= admin_chip_os_softwares_path %>">软件及版本号</a>
                    </dd>
                  <% end %>
                </dl>
              </dd>
            <% end %>

          </dl>
        </li>
      <% end %>

      <!-- <li data-name="questions" class="layui-nav-item">
        <a href="javascript:;" lay-tips="应用" lay-direction="2">
          <i class="layui-icon layui-icon-app"></i>
          <cite>问答</cite>
        </a>
        <dl class="layui-nav-child">

          <dd data-name="user_questions">
            <a href="javascript:;">问答记录</a>
            <dl class="layui-nav-child">
              <dd data-name="user_questions_list"><a lay-href="<%#=  %>">问答列表</a></dd>
              <dd data-name="data_annotation"><a lay-href="">数据标注</a></dd>
            </dl>
          </dd>
          <dd data-name="user_evaluates">
            <a href="javascript:;">评价记录</a>
            <dl class="layui-nav-child">
              <dd data-name="evaluate_list"><a lay-href="<%#=  %>">评价内容</a></dd>
              <dd data-name="evaluate_rules"><a lay-href="">评价规则</a></dd>
            </dl>
          </dd>
        </dl>
      </li> -->

    </ul>
  </div>
</div>