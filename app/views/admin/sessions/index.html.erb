<%= javascript_include_tag 'admin/application' %>
<%= stylesheet_link_tag "admin/tailwind.min" %>
<%= stylesheet_link_tag "admin/login" %>
<div class="relative min-h-screen flex">
    <div
        class="
            flex flex-col
            sm:flex-row
            items-center
            md:items-start
            sm:justify-center
            md:justify-start
            flex-auto
            min-w-0
            bg-white
        "
    >
        <div
            class="
                sm:w-1/2
                xl:w-3/5
                h-full
                hidden
                md:flex
                flex-auto
                items-center
                justify-center
                p-10
                overflow-hidden
                bg-purple-900
                text-white
                bg-no-repeat bg-cover
                relative
            "
            style="background-image: url('<%= image_url "ai3.png" %>');"

        >
            <div
                class="
                    absolute
                    bg-gradient-to-b
                    from-indigo-600
                    to-blue-500
                    opacity-75
                    inset-0
                    z-0
                "
            ></div>
            <div class="w-full max-w-md z-10">
                <div class="sm:text-4xl xl:text-5xl font-bold leading-tight mb-6"
                    >项目管理系统</div>

                <div class="sm:text-sm xl:text-md text-gray-200 font-normal" id="output">
                  <span class="cursor" id="cursor"></span>

                </div>
                <div id="givenText" style="display: none;">
                  项目管理系统，致力于Seven团队打造，专注于精品设计
                </div>
            </div>
            <ul class="circles">
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
            </ul>
        </div>
        <div
            class="
                md:flex md:items-center md:justify-center
                w-full
                sm:w-auto
                md:h-full
                w-2/5
                xl:w-2/5
                p-8
                md:p-10
                lg:p-14
                sm:rounded-lg
                md:rounded-none
                bg-white
            "
        >
            <div class="max-w-md w-full mx-auto space-y-8">
                <div class="text-center">
                    <h2 class="mt-6 text-3xl font-bold text-gray-900">欢迎回来</h2>
                    <p class="mt-2 text-sm text-gray-500">第三方登录</p>
                </div>
                <div class="flex flex-row justify-center items-center space-x-3">
                    <a
                        href="javascript:"

                        class="
                            w-11
                            h-11
                            items-center
                            justify-center
                            inline-flex
                            rounded-2xl
                            font-bold
                            text-lg
                            bg-blue-900
                            hover:shadow-lg
                            cursor-pointer
                            transition
                            ease-in
                            duration-300
                            weixin-backgound
                        "
                        ><img
                            class="w-4 h-4"
                            src="<%= image_path '微信.png'%>"
                    /></a>
                    <a
                        href="javascript:"
                        class="
                            w-11
                            h-11
                            items-center
                            justify-center
                            inline-flex
                            rounded-2xl
                            font-bold
                            text-lg text-white
                            bg-blue-400
                            hover:shadow-lg
                            cursor-pointer
                            transition
                            ease-in
                            duration-300
                        "
                        ><img class="w-4 h-4" src="<%= image_path 'QQ.png'%>" /></a>
                    <a
                        href="javascript:"
                        class="
                            w-11
                            h-11
                            items-center
                            justify-center
                            inline-flex
                            rounded-2xl
                            font-bold
                            text-lg text-white
                            bg-blue-500
                            hover:shadow-lg
                            cursor-pointer
                            transition
                            ease-in
                            duration-300
                            qyweixin-backgound
                        "
                        ><img
                        src="<%= image_path '企业微信.png'%>"
                            class="w-4 h-4"
                    /></a>
                </div>
                <div class="flex items-center justify-center space-x-2">
                    <span class="h-px w-16 bg-gray-200"></span>
                    <span class="text-gray-300 font-normal">请使用用户名登录</span>
                    <span class="h-px w-16 bg-gray-200"></span>
                </div>
                <form class="mt-8 space-y-6" action="<%= login_user_admin_sessions_path %>" method="POST">
                    <input type="hidden" name="authenticity_token" value="<%= form_authenticity_token %>" autocomplete="off">
                    <input type="hidden" name="remember" value="true" />
                    <div class="relative">
                        <label class="ml-3 text-sm font-bold text-gray-700 tracking-wide"
                            >用户名/手机号</label
                        >
                        <input
                            class="
                                w-full
                                text-base
                                px-4
                                py-2
                                border-b border-gray-300
                                focus:outline-none
                                rounded-2xl
                                focus:border-indigo-500
                            "
                            type=""
                            placeholder="请输入用户名/手机号"
                            name="phone"
                            value="<%= params[:phone]%>"
                        />
                    </div>
                    <div class="mt-8 content-center">
                        <label class="ml-3 text-sm font-bold text-gray-700 tracking-wide"
                            >密码</label
                        >
                        <input
                            class="
                                w-full
                                content-center
                                text-base
                                px-4
                                py-2
                                border-b
                                rounded-2xl
                                border-gray-300
                                focus:outline-none focus:border-indigo-500
                            "
                            type="password"
                            placeholder="请输入密码"
                            name="password"
                        />
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input
                                id="remember_me"
                                name="remember_me"
                                type="checkbox"
                                class="
                                    h-4
                                    w-4
                                    bg-blue-500
                                    focus:ring-blue-400
                                    border-gray-300
                                    rounded
                                "
                            />
                            <label
                                for="remember_me"
                                class="ml-2 block text-sm text-gray-900"
                                >记住我</label
                            >
                        </div>
                        <div class="text-sm">
                            <a href="#" class="text-indigo-400 hover:text-blue-500"
                                >忘记密码？</a
                            >
                        </div>
                    </div>
                    <div>
                        <button
                            type="submit"
                            class="
                                w-full
                                flex
                                justify-center
                                bg-gradient-to-r
                                from-indigo-500
                                to-blue-600
                                hover:bg-gradient-to-l
                                hover:from-blue-500
                                hover:to-indigo-600
                                text-gray-100
                                p-4
                                rounded-full
                                tracking-wide
                                font-semibold
                                shadow-lg
                                cursor-pointer
                                transition
                                ease-in
                                duration-500
                            "
                            >登 录</button
                        >
                    </div>
                    <p
                        class="
                            items-center
                            justify-center
                            mt-10
                            text-center text-md text-gray-500
                        "
                    >
                        <span>还没有账号？</span>
                        <a
                            href="javascript:"
                            target="_blank"
                            class="
                                text-indigo-400
                                hover:text-blue-500
                                no-underline
                                hover:underline
                                cursor-pointer
                                transition
                                ease-in
                                duration-300
                            "
                            >请联系管理员注册</a
                        >
                    </p>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
  function typeText() {
    const outputElement = $("#output");
    const cursorElement = $("#cursor");
    const givenTextElement = $("#givenText");
    const givenText = givenTextElement.html();
    let currentIndex = 0;
    let currentHTML = "";

    function animateTyping() {
      if (currentIndex < givenText.length) {
        const currentChar = givenText.charAt(currentIndex);
        if (currentChar === "<") {
          const closingTagIndex = givenText.indexOf(">", currentIndex);
          currentHTML += givenText.slice(currentIndex, closingTagIndex + 1);
          currentIndex = closingTagIndex + 1;
        } else {
          currentHTML += currentChar;
          currentIndex++;
        }

        outputElement.html(currentHTML);
        setTimeout(animateTyping, 25); // 设置打字速度，单位为毫秒
      } else {
        // 当打印完成时，移除光标的闪烁效果
        cursorElement.removeClass("cursor");
      }
    }
    animateTyping();
  }

  $(document).ready(function() {
    typeText();
  });
</script>