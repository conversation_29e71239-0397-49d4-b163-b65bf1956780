<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>
    <table id="table_project_risks" lay-filter="admin-table"></table>
  </div>
</div>

<%= javascript_include_tag 'admin/welcome.js'%>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_project_risks',
      url: `/admin/project_risks?project_id=<%= @project.id %>&q%5Bproject_id_eq%5D=<%= @project.id %>`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      cols: [[

          // {field: 'project_id', align:'left', title: '项目ID', minWidth: 100},

          {field: 'name', align:'left', title: '风险名称', minWidth: 300, templet: function (d)
          {
            return `<a href="/admin/project_risks/${d.id}" data-remote='true' style='color: #2468f2;'>${d.name}</a>`
          }
        },

          {field: 'risk_type', align:'left', title: '风险类型', minWidth: 100},

          // {field: 'which_module', align:'left', title: '所属模块', minWidth: 100},

          // {field: 'description', align:'left', title: '风险描述', minWidth: 100},

          {field: 'risk_level', align: 'left', title: '风险等级', width: 90, templet: function (d) {
            return `<span class="seven-tag ${d.risk_level[1]}">${d.risk_level[0]}</span>`;
          }},

          {field: 'nature', align:'left', title: '风险性质', minWidth: 100},

          {field: 'problem_severity', align:'left', title: '影响程度', minWidth: 100},

          {field: 'aasm_state', align:'left', title: '状态', minWidth: 100},

          {field: 'started_at', align:'left', title: '起始时间', width: 180},

          {field: 'ended_at', align:'left', title: '结束时间', width: 180},

          {fixed: 'right', align: 'left', title: '操作', minWidth: 190, templet: function (d)
          {
            var button = '';
            if (d.status == 'identified'){
              button += `<a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>`
              button += `<a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>`
            }
            d.button.forEach(res => {
              if (res.child && res.child.length > 0){
                res.child.forEach(child_res => {
                  button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_detail_${child_res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${child_res.id}">${res.title}${child_res.title}</a>`
                })
              }else{
                button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_${res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${res.id}">${res.title}</a>`
              }
            });
            return button;
          }
        }
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/project_risks/${data.id}/edit?project_id=<%= @project.id %>`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/project_risks/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){
        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/project_risks/${data.id}`,
            data: {
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/project_risks/new?project_id=<%= @project.id %>`,
            data: {
            }
          })
        break;
      };
    });

  })
</script>