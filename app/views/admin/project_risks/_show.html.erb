<!-- 添加 seven-container 作为作用域容器 -->
<div class="seven-container">
  <div class="seven-flex-container">
    <!-- 左侧详情面板 -->
    <div class="seven-detail-panel">
      <div class="seven-card-header">
        <div class="seven-card-title">
          <span>风险详情</span>
        </div>

        <div class="seven-action-buttons">
          <% @project_risk.get_button(current_user).each do |button| %>
            <a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal project_risks_<%= button[:id] %>"
               data-uuid="<%= @project_risk.id %>"
               data-project_id="<%= @project_risk.project_id %>"
               data-event="<%= button[:id]%>">
              <%= button[:title]%>
            </a>
          <% end %>
        </div>
      </div>
      <div class="seven-card-body">
        <div class="seven-ticket-info-grid">
          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:name) %></div>
            <div class="seven-info-value"><%= @project_risk.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:project_id) %></div>
            <div class="seven-info-value"><%= @project_risk.project.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:risk_type) %></div>
            <div class="seven-info-value"><%= @project_risk.risk_type_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:which_module) %></div>
            <div class="seven-info-value"><%= @project_risk.which_module %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">创建人</div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= @project_risk.user&.name.to_s.first %>
                </div>
                <span><%= @project_risk.user.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:obligation_user_id) %></div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= (@project_risk.obligation_user&.name || '无').to_s.first %>
                </div>
                <span><%= @project_risk.obligation_user&.name %></span>
              </div>
            </div>
          </div>


          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:aasm_state) %></div>
            <div class="seven-info-value">
              <span class="seven-tag seven-tag-blue"><%= @project_risk.aasm_state_i18n %></span>
            </div>
          </div>

          <% if @project_risk.riskable.present? %>
            <div class="seven-info-item">
              <div class="seven-info-label">转化的任务</div>
              <div class="seven-info-value">
                <span class="seven-info-value"><a style="color: #2468f2;" href="/admin/<%= @project_risk.riskable_type.underscore.pluralize %>/<%= @project_risk.riskable_id %>" data-remote="true"><%= @project_risk.riskable.name %></a></span>
              </div>
            </div>
          <% end %>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:risk_level) %></div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @project_risk.risk_level_i18n %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:nature) %></div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @project_risk.nature_i18n %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:problem_severity) %></div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @project_risk.problem_severity_i18n %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:coping_strategy) %></div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @project_risk.coping_strategy %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:started_at) %></div>
            <div class="seven-info-value"><%= @project_risk.started_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:ended_at) %></div>
            <div class="seven-info-value"><%= @project_risk.ended_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:act_started_at) %></div>
            <div class="seven-info-value"><%= @project_risk.act_started_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:act_ended_at) %></div>
            <div class="seven-info-value"><%= @project_risk.act_ended_at&.strftime("%F %T") %></div>
          </div>

        </div>

        <hr style="margin: 16px 0; border-color: rgba(0, 0, 0, 0.06);">

        <div class="seven-info-item">
          <div class="seven-info-label">问题描述</div>
          <div class="seven-description-box">
            <%= @project_risk.description&.html_safe %>
          </div>
        </div>

        <div class="seven-info-item">
          <div class="seven-info-label"><%= ProjectRisk.human_attribute_name(:action_plan) %></div>
          <div class="seven-description-box">
            <%= @project_risk.action_plan %>
          </div>
        </div>

        <div class="seven-info-item">
          <div class="seven-info-label">关闭原因</div>
          <div class="seven-description-box">
            <%= @project_risk.close_reason %>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="seven-side-panel">
      <!-- 变动记录卡片 -->
      <div class="seven-card">
        <div class="seven-card-header">
          <div class="seven-card-title">
            <span>变动记录</span>
          </div>
        </div>
        <div class="seven-card-body">
          <div class="seven-timeline">
            <% @project_risk.build_change_log.each do |log| %>
              <div class="seven-timeline-item">
                <div class="seven-timeline-dot"></div>
                <div class="seven-timeline-date"><%= log[:change_at] %></div>
                <div class="seven-timeline-content">
                  <% if log[:change_type]=='create' %>
                    <p>创建任务</p>
                  <% else %>
                    <% log[:changes].each do |change| %>
                      <% if change[:from].present? %>
                        <p>
                          <span style='color: #1677ff'>
                            <%= change[:field] %>
                          </span>: <br>
                          由 "<span style='color: #faad14'>
                            <%= change[:from] %>
                          </span>"
                          变动为"<span style='color: #52c41a'>
                            <%= change[:to] %>
                          </span>"
                        </p>
                      <% else %>
                        <span style='color: #1677ff'>
                          <%= change[:field] %>
                        </span>:<br>变动为
                        "<span style='color: #52c41a'>
                          <%= change[:to] %>
                        </span>"<br>
                      <% end %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- 评论卡片 -->
      <div id="<%= @commentable_type %>_<%= @commentable_id %>_comments" class="seven-card" style="flex: 2;"><%= render 'admin/comments/show'%></div>
    </div>
  </div>
</div>

<%= javascript_include_tag 'admin/welcome.js'%>
