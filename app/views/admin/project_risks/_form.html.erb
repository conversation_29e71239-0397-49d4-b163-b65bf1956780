<%= simple_form_for([:admin, @project_risk],  remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>

    <%= f.input :project_id, collection: Project.all, selected: @project.id, wrapper_html: {style: 'display:none'} %>
    <%#= f.input :category, input_html: {'lay-verify': "required"} %>
    <%= f.input :name , input_html: {'lay-verify': "required"}%>
    <div class="flex">
      <%= f.input :risk_type, input_html: {'lay-verify': "required"} %>
      <%= f.input :which_module, input_html: {'lay-verify': "required"} %>
    </div>

    <%= f.input :description, as: :text %>
    <div class="flex">
      <%= f.input :risk_level, input_html: {'lay-verify': "required"} %>
      <%= f.input :nature, input_html: {'lay-verify': "required"} %>
    </div>
    <div class="flex">
      <%= f.input :problem_severity, input_html: {'lay-verify': "required"}, label: "严重程度" %>
      <%= f.input :coping_strategy, input_html: {'lay-verify': "required"} %>
    </div>

    <%= f.input :action_plan, input_html: {'lay-verify': "required", class: "layui-textarea"} %>
    <%= f.input :started_at, input_html: {'lay-verify': "required", class: 'started_at', autocomplete: 'off', value: f.object.started_at&.strftime("%Y-%m-%d %H:%M:%S") || Time.now.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择期望结束时间", readonly: true, unit: "&#xe637;" %>
    <%= f.input :ended_at, input_html: {'lay-verify': "required", class: 'ended_at', autocomplete: 'off', value: f.object.ended_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择期望结束时间", readonly: true, unit: "&#xe637;" %>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  BuildFroalaEditor('#project_risk_description');
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      var laydate = layui.laydate;
      laydate.render({
        elem: '.ended_at',
        type: 'datetime',
        fullPanel: true
      });
      laydate.render({
        elem: '.started_at',
        type: 'datetime',
        fullPanel: true
      });
      form.render();
    });
  });
</script>