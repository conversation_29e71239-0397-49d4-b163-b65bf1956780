<%= simple_form_for([:admin, @plan], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>
    <%= f.input :name, label: '名称', input_html: {'lay-verify': "required"} %>
    <%= f.input :content, as: :text, label: '内容', input_html: {'class': "layui-textarea"} %>
    <div class="project-form">
      <%= f.input :started_at, label: '开始时间', input_html: {'lay-verify': "required", class: 'started_at', autocomplete: 'off', value: f.object.started_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择计划开始时间", readonly: true, unit: "&#xe637;"%>
      <%= f.input :ended_at, label: '结束时间', input_html: {'lay-verify': "required", class: 'ended_at', autocomplete: 'off', value: f.object.ended_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择计划结束时间", readonly: true, unit: "&#xe637;"%>
    </div>

    <div class="project-form">
      <%= f.input :status, label: '状态', input_html: {'lay-verify': "required"} %>
      <%= f.input :p_priority, label: '优先级', input_html: {'lay-verify': "required"} %>
    </div>

    <div class="project-form">
      <%= f.input :p_type, label: '类型', input_html: {'lay-verify': "required"} %>
      <%= f.input :duty_user_id, label: '责任人', collection: @users, input_html: {'lay-verify': "required"}%>
    </div>

    <%= f.input :parent_id, label: '父级计划', collection: @parents, input_html: {"lay-filter": "select_parent_id"} %>
    <%= f.input :project_id, input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :user_id, input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :organization_id, label: '组织ID', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :sort_ids, wrapper_html: {style: 'display:none'}%>
  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  BuildFroalaEditor('#plan_content');
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
  var layer = layui.layer,
      form = layui.form;
  var laydate = layui.laydate;

  // 初始化日期选择器
  initDatePickers();

  // 监听 select 变化事件
  form.on('select(select_parent_id)', function(data){
    var selectedValue = data.value;

    if(selectedValue.length === 0) {
      resetDatePickers();
      return;
    }

    $.ajax({
      url: '/admin/plans/search_parent_time',
      type: 'GET',
      data: {
        id: selectedValue,
        project_id: '<%= @project.id %>'
      },
      success: function(res) {
        updateDatePickers(res.data.started_at, res.data.ended_at);

        var startedAt = $('.started_at').val();
        var endedAt = $('.ended_at').val();

        var showMessage = false;
        var message = '';

        if (startedAt && (startedAt < res.data.started_at || startedAt > res.data.ended_at)) {
          $('.started_at').val('');
          showMessage = true;
          message += '开始时间';
        }
        if (endedAt && (endedAt < res.data.started_at || endedAt > res.data.ended_at)) {
          $('.ended_at').val('');
          showMessage = true;
          if (message) {
            message += '和';
          }
          message += '结束时间';
        }

        if (showMessage) {
          layer.msg('受上级计划时间范围限制，' + message + '已被重置');
        }
      }
    });
  });

  // 渲染表单
  form.render();

  // 初始化日期选择器
  function initDatePickers() {
    renderDatePicker('.started_at');
    renderDatePicker('.ended_at');
  }

  // 重置日期选择器为默认状态
  function resetDatePickers() {
    renderDatePicker('.started_at');
    renderDatePicker('.ended_at');
  }

  // 更新日期选择器的范围
  function updateDatePickers(min, max) {
    renderDatePicker('.started_at', { min: min, max: max });
    renderDatePicker('.ended_at', { min: min, max: max });
  }

  // 渲染日期选择器的通用函数
  function renderDatePicker(selector, options = {}) {
    var defaultOptions = {
      elem: selector,
      type: 'datetime',
      fullPanel: true,
      min: `<%= @min_started_at %>`,
      max: `<%= @max_ended_at %>`
    };

    if (selector === '.ended_at') {
      defaultOptions.value = '';
      defaultOptions.isInitValue = false;
    }

    laydate.render(Object.assign({}, defaultOptions, options));
  }
});




  });
</script>