<div class="layui-card">
  <div class="layui-card-body">
    <table id="table_projects" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <% if policy(Project).update? %>
  {{#  if(d.editable ){ }}
    <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  {{#  } }}
  <% end %>
  <% if policy(Project).show? %>
  <a lay-href="/admin/projects/{{=d.id}}" lay-title="{{=d.name}}的项目详情" class="layui-btn layui-btn-xs layui-border-orange layui-btn-primary"><i class="layui-icon layui-icon-eye"></i></a>
  <% end %>
  <% if policy(Project).destroy? %>
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
  <% end %>
</script>
<% if policy(Project).create? %>
<script type="text/html" id="myBar">
  <button class="layui-btn jh-btn-normal layui-btn-sm new_projects" data-button="<%= current_organization.project_create_button.to_json %>" lay-options="{trigger: 'hover'}">
    <i class="layui-icon layui-icon-add-1"></i>
    新建项目
    <i class="layui-icon layui-icon-down layui-font-12"></i>
  </button>
</script>
<% end %>
<script>
  var table;
  layui.use(function(){
    table = layui.table
    var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_projects',
      url: `/admin/projects`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      cols: [[
          {field: 'name', align:'left', title: '名称', minWidth: 300, templet: function (d)
            {
              return `<a lay-href="/admin/projects/${d.id}" lay-title="${d.name}的项目详情" style='color: #2468f2;'>${d.name}</a>`
            }
          },

          {field: 'chip_platform', align:'left', title: '芯片平台', width: 100},

          {field: 'project_type', align:'left', title: '项目类型', width: 100},

          {field: 'evt_at', align:'left', title: 'Evt时间', width: 120},

          {field: 'dvt_at', align:'left', title: 'Dvt时间', width: 120},

          {field: 'pvt_at', align:'left', title: 'Pvt时间', width: 120},

          {field: 'mp_at', align:'left', title: 'MP时间', width: 120},

          {field: 'status', align:'left', title: '状态', width: 100},

          {field: 'username', align:'left', title: '创建人', width: 100},

          {field: 'team_size', align:'left', title: '项目人数', width: 100},

          {field: 'organization_name', align:'left', title: '所属组织', width: 100},

        {fixed: 'right', title: '操作', minWidth: 170, align: 'left', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        if (data.type == 'p_internal'){ // 内部方案
          $.ajax({
            type: 'GET',
            url: `/admin/projects/${data.id}/edit`,
            data: {
            }
          })
        }else if (data.type == 'p_support'){ // 技术支持案
          $.ajax({
            type: 'GET',
            url: `/admin/projects/edit_support`,
            data: {
              id: data.id
            }
          })
        }else if (data.type == 'p_joint'){ // 联合开发案
          $.ajax({
            type: 'GET',
            url: `/admin/projects/edit_joint`,
            data: {
              id: data.id
            }
          })
        }
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/projects/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){
        layer.prompt({title: `请输入项目名称“${data.name}”，用于确认删除`}, function(value, index, elem){
          if(value === '') return elem.focus();
          // 关闭 prompt
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/projects/${data.id}`,
            data: {
              'confirm_name': value
            }
          })
        });
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/projects/new`,
            data: {
            }
          })
        break;
      };
    });

    var dropdown = layui.dropdown;
    // 自定义事件
    var createButton = JSON.parse($('.new_projects').attr('data-button'));
    dropdown.render({
      elem: '.new_projects',
      // trigger: 'click' // trigger 已配置在元素 `lay-options` 属性上
      data: createButton,
      click: function(data, othis, event){
        if (data.type=='p_internal'){
          $.ajax({
            type: 'GET',
            url: `/admin/projects/new`,
            data: {
              type: data.type
            }
          })
        }else if (data.type=='p_support'){
          $.ajax({
            type: 'GET',
            url: `/admin/projects/new_support`,
            data: {
              type: data.type
            }
          })
        }else{
          $.ajax({
            type: 'GET',
            url: `/admin/projects/new_joint`,
            data: {
              type: data.type
            }
          })
        }
      }
    });
  })
</script>