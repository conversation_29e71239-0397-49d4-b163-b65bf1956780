<!-- 评论卡片 -->
<div class="seven-card-header">
  <div class="seven-card-title">
    <span>评论</span>
  </div>
</div>
<div class="seven-card-body" style="display: flex; flex-direction: column; padding: 0px 16px;">
  <div id="seven-comments-container" style="flex: 1; overflow-y: auto; padding-bottom: 16px;">
    <!-- 循环渲染根评论 -->
    <% @comment_tree.each do |comment| %>
      <%= render 'admin/comments/comment', comment: comment %>
    <% end %>
  </div>

  <!-- 固定在底部的评论输入框 -->
  <div class="seven-comment-form" style="margin-top: auto; border-top: 1px solid rgba(0, 0, 0, 0.06); padding-top: 1px;">
    <div class="seven-reply-indicator" style="display: none; margin-bottom: 8px; font-size: 12px; color: #1677ff;">
      回复给：<span id="seven-reply-to-user"></span>
      <button id="seven-cancel-reply" style="margin-left: 8px; color: #999; background: none; border: none; cursor: pointer;">取消</button>
    </div>
    <textarea id="seven-comment-input" placeholder="在此输入您的评论..."></textarea>
    <div style="display: flex; justify-content: flex-end;">
      <button id="seven-submit-comment">提交评论</button>
    </div>
  </div>
</div>

<script>
  // 获取评论对象信息（根据你的业务调整）
  var commentableType = '<%= @commentable_type %>';
  var commentableId = '<%= @commentable_id %>';

  $(document).ready(function() {
    var comment_editor = CommentEditor('#seven-comment-input');
    var parentId = ''; // 初始化 parent_id

    // 回复按钮点击事件
    $(document).off('click', '.seven-reply-btn').on('click', '.seven-reply-btn', function () {
      var userName = $(this).data('user');
      $('#seven-reply-to-user').text(userName);
      $('.seven-reply-indicator').show();
      $('#seven-comment-input').attr('placeholder', '回复给 ' + userName + '...').focus();
      // 更新 parentId 为当前被回复的评论 ID
      $('#seven-submit-comment').attr('data-id', $(this).data('id'))
    });

    // 取消回复按钮点击事件
    $(document).off('click', '#seven-cancel-reply').on('click', '#seven-cancel-reply', function () {
      $('.seven-reply-indicator').hide();
      $('#seven-comment-input').attr('placeholder', '在此输入您的评论...');
      $('#seven-submit-comment').attr('data-id', '')
    });

    // 提交评论功能
    $(document).off('click', '#seven-submit-comment').on('click', '#seven-submit-comment', function () {
      var commentText = $('#seven-comment-input').val().trim();
      if (!commentText) {
        layer.msg('请输入评论内容');
        return;
      }

      var replyTo = $('#seven-reply-to-user').text();
      var isReply = replyTo !== '';

      $.ajax({
        url: '/admin/comments', // 替换为你的后端接口
        method: 'POST',
        data: {
          comment: {
            content: commentText,
            commentable_type: commentableType,
            commentable_id: commentableId,
            parent_id: $('#seven-submit-comment').attr('data-id')
          }
        }
      });
    });
  });

  // 删除
  $(document).off('click', '.seven-delete-btn').on('click', '.seven-delete-btn', function () {
      var id = $(this).data('id');
      layer.confirm("确认此评论吗，删除后子评论也会连带删除？", function(index){
        $.ajax({
          type: 'DELETE',
          url: `/admin/comments/${id}`,
          data: {
            commentable_type: commentableType,
            commentable_id: commentableId,
          }
        })
        layer.close(index);
      });
    });

</script>