<%= simple_form_for([:admin, @organization_flow], remote: true, html: {class: 'layui-form'}, wrapper: :seven_form_line) do |f| %>
  <%= f.error_notification %>
  <%= f.input :name, label: '名称', input_html: {'lay-verify': "required"} %>
  <%= f.input :description, as: :text, label: '流程描述', input_html: {'class': "layui-textarea"} %>
  <%= f.input :flow_type, label: '类型', input_html: {'lay-verify': "required"} %>
  <%= f.input :user_id, input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
  <%= f.input :organization_id, label: '组织ID', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>


  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>