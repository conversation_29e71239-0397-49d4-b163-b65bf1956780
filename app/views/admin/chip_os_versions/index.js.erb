layer.open({
	type: 1,
  id: 'version_config',
	title: '配置版本号',
	area: ['70%', '85%'],
	shade: 0.5,
	maxmin: true,
	offset: 'auto',
	content: `<div class="layui-card">
              <div class="layui-card-body">
                <table id="table_chip_os_versions" lay-filter="version_config-table"></table>
              </div>
            </div>

            <script type="text/html" id="barDemo2">
                <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
                <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
              </script>
              <script type="text/html" id="myBar2">
                <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
              </script>`,
  success:function(){
    layui.use(function(){
    var table = layui.table
    var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'version_config',
      elem: '#table_chip_os_versions',
      url: `/admin/chip_os_versions?chip_os_software_id=<%= params[:chip_os_software_id] %>`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar2',
      defaultToolbar: [],
      cols: [[
        {field: 'is_default', align:'left', title: '是否默认版本', minWidth: 100},
        {field: 'version', align:'left', title: '版本号', minWidth: 100},
        {fixed: 'right', title: '', minWidth: 170, align: 'left', toolbar: '#barDemo2'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(version_config-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/chip_os_versions/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/chip_os_versions/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/chip_os_versions/${data.id}`,
            data: {
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(version_config-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/chip_os_versions/new?chip_os_software_id=<%= params[:chip_os_software_id] %>`,
            data: {
            }
          })
        break;
      };
    });
  })
  }
});
