layer.open({
  title: '审批详情',
  type: 1,
  offset: 'r',
  anim: 'slideLeft',
  area: ['320px', '100%'],
  shade: 0.1,
  shadeClose: true,
  id: 'show_approval_step',
  content: `<div style="padding: 16px;">
    <div class="layui-timeline">
      <% @approval_step.approval_step_users.order(:order_number).each do |approval_step_user| %>
        <div class="layui-timeline-item">
          <%= approval_step_user.status_color[1].html_safe %>
          <div class="layui-timeline-content layui-text">
            <div><%= approval_step_user.user.name %></div>
            <% if approval_step_user.approved? || approval_step_user.rejected? %>
              <div><%= approval_step_user.operating_at.strftime('%F %T') %></div>
              <div><%= approval_step_user.status_i18n %>(<%= approval_step_user.review_comment %>)</div>
            <% else %>
              <div style='color: <%= approval_step_user.status_color[0] %>'><%= approval_step_user.status_i18n %></div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>

  </div>`
});