<%= simple_form_for([:admin, @project_role_config], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>
  <%= f.input :name, label: '名称', input_html: {'lay-verify': "required"}%>

  <%= f.input :organization_id, wrapper_html: {style: 'display: none'} %>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">权限</label>
      <div class="layui-input-block flex-item">
        <div id="select_role" style="width: 99%;" data-value="<%= @select_data.to_json %>"></div>
      </div>
    </div>
  </div>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
        layer = layui.layer,
        layuiTable = layui.table;
        form = layui.form;
        form.render();
    });
  });

  var select_role;
  layui.use(['xmSelect'], function () {
    var xmSelect = layui.xmSelect
    //渲染数据
    select_role = xmSelect.render({
      el: '#select_role',
      autoRow: true,
      toolbar: { show: true },
      tips: '请选择权限',
      filterable: true,
      data: JSON.parse($('#select_role').attr('data-value'))
    })
    select_role.getValue();
  })

</script>