<div class="layui-card">
  <div class="layui-card-body">
    <table id="table_project_role_configs" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
</script>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
    table = layui.table
    var layer = layui.layer //弹层

    table.render({
      id: 'listPage',
      elem: '#table_project_role_configs',
      url: `/admin/project_role_configs`, //数据接口
      title: '列表',
      skin: 'line',
      page: false, //开启分页
      toolbar: '#myBar',
      cols: [[,
        {field: 'id', title: '编号', width: 120, sort: true, fixed: 'left', hide: true},
        {field: 'name', align: 'left', title: '角色', width: 160 },
        {field: 'is_default', align: 'left', title: "默认角色 <i class='layui-icon layui-icon-tips tip_content'></i>", width: 110, templet: function (d)
          {
            if (d.is_default){
              html = '<span>默认<span>'
            }else{
              html = "<a href='javascript:void(0)' style='color: #FF6941 !important;' class='set_default_role'><i class='layui-icon layui-icon-set-fill'></i>设为默认</a>"
            }
            return html
          }
        },
        {field: 'permissions', align: 'left', minWidth: 220, title: '所拥有的权限' },
        {fixed: 'right', title: '', width: 170, align: 'center', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/project_role_configs/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/project_role_configs/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/project_role_configs/${data.id}`,
            data: {
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/project_role_configs/new`,
            data: {
            }
          })
        break;
      };
    });

  })

  $(document).on('mouseover', '.tip_content', function(){
    layer_tips = layer.tips('默认角色是创建项目时，项目创建者在项目中的默认角色和身份。', this, {
      time: 0,
      tips: [1, "#393D49"]
    });
  })

  $(document).on('mouseleave', '.tip_content', function(){
    layer.close(layer_tips);
  })

  $(document).on('click', '.set_default_role', function(){
    var id = $(this).parents('tr').find('td[data-field="id"] div').text();
    console.log(id)
    layer.confirm('确认修改当前角色为默认身份角色吗？并将其他角色设为“非默认”状态', {id: 'delete_confirm', zIndex: 999999999999},function(index){
      layer.close(index);
      $.ajax({
        type: 'POST',
        url: '/admin/project_role_configs/update_is_default',
        data: {
          id: id
        }
      })
    });
  })
</script>