<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>
    <table id="table_work_orders" lay-filter="admin-table"></table>
  </div>
</div>

<!-- index留存了原有写法 -->
<%= javascript_include_tag 'admin/welcome.js'%>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
    table = layui.table
    var layer = layui.layer //弹层
      ,laypage = layui.laypage //分页
      ,page = 1//页码全局变量
      ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_work_orders',
      url: `/admin/work_orders?project_id=<%= @project.id %>&q%5Bproject_id_eq%5D=<%= @project.id %>&q%5Bwork_type_eq%5D=2`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      cols: [[
        // {field: 'project_name', align:'left', title: '项目名称', minWidth: 100},
        // {field: 'work_type', align:'left', title: '工单类型', minWidth: 100},
        {field: 'title', align:'left', title: '标题', minWidth: 300, templet: function (d)
          {
            return `<a href="/admin/work_orders/${d.id}" data-remote='true' style='color: #2468f2;'>${d.title}</a>`
          }
        },

        // {field: 'description', align:'left', title: '描述', minWidth: 100},
        // {field: 'hardware_version', align:'left', title: '硬件版本', minWidth: 100},
        // {field: 'problem_severity', align:'left', title: '问题严重程度', minWidth: 100},
        {field: 'priority', align: 'left', title: '优先级', width: 80, templet: function (d) {
          return `<span class="seven-tag ${d.priority[1]}">${d.priority[0]}</span>`;
        }},
        {field: 'which_module', align:'left', title: '所属模块', minWidth: 100},
        {field: 'aasm_state', align:'left', title: '状态', width: 100},
        {field: 'chip_config_id', align:'left', title: '芯片平台', minWidth: 100},
        {field: 'chip_os_software_id', align:'left', title: 'OS系统', minWidth: 100},
        {field: 'chip_os_version_id', align:'left', title: '软件版本', minWidth: 100},
        {field: 'product_category_id', align:'left', title: '产品类型', minWidth: 100},
        {field: 'product_name', align:'left', title: '产品名称', minWidth: 100},
        // {field: 'customer_name', align:'left', title: '客户名称', minWidth: 100},
        // {field: 'project_progress', align:'left', title: '项目进度', minWidth: 100},
        // {field: 'repro_steps', align:'left', title: '重现步骤', minWidth: 100},
        // {field: 'file', align:'left', title: '附件', minWidth: 100},
        {field: 'started_at', align:'left', title: '开始时间', width: 170},
        {field: 'ended_at', align:'left', title: '结束时间', width: 170},
        {field: 'founder_id', align:'left', title: '创建人', width: 100},
        {field: 'receiver_user_id', align:'left', title: '评估人', width: 100},
        {field: 'obligation_user_id', align:'left', title: '责任人', width: 100},
        {field: 'created_at', align:'left', title: '创建时间', width: 170},
        {fixed: 'right', align: 'left', title: '操作', minWidth: 190, templet: function (d)
          {
            var button = '';
            if (d.edit_delete_status){
              button += `<a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>`
              button += `<a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>`
            }
            d.button.forEach(res => {
              if (res.child && res.child.length > 0){
                res.child.forEach(child_res => {
                  button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_detail_${child_res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${child_res.id}">${res.title}${child_res.title}</a>`
                })
              }else{
                button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_${res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${res.id}">${res.title}</a>`
              }
            });
            return button;
          }
        }
      ]]
    });

    //监听行工具事件
    table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
        ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit?project_id=<%= @project.id %>&tag=work_bug`,
          data: {
          }
        })
      }else if (layEvent === 'show'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/work_orders/${data.id}`,
            data: {
            }
          })
        })
      }

    });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
        ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/work_orders/new?project_id=<%= @project.id %>&tag=work_bug`,
            data: {
            }
          })
          break;
      };
    });

  })
</script>