<!--工单局部表单：解决-->
<%= simple_form_for([:admin, @work_order], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>

  <%= f.input :solution, as: :text %>
  <%= f.input :button_tag, input_html: {value: "solved"},wrapper_html: {style: 'display:none'} %>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });



</script>