<% case @tag %>
<% when "work_bug" %>
  layer.open({
    id: 'new_open',
    type: 1,
    title: '新增<%= WorkOrder.model_name.human %>',
    area: ['50%', '85%'],
    shade: 0.5,
    maxmin: true,
    offset: 'auto',
    content: "<%= escape_javascript(render 'bug_form') %>",
    btn: ['保存', '关闭'],
    yes: function(){
      $('.save_btn').click();
    },
		success: function(layero, index){
			layer.full(index); // 最大化
		}
  });
<% when "work_demand" %>
  layer.open({
    id: 'new_open',
    type: 1,
    title: '新增<%= WorkOrder.model_name.human %>',
    area: ['50%', '85%'],
    shade: 0.5,
    maxmin: true,
    offset: 'auto',
    content: "<%= escape_javascript(render 'demand_form') %>",
    btn: ['保存', '关闭'],
    yes: function(){
      $('.save_btn').click();
    },
		success: function(layero, index){
			layer.full(index); // 最大化
		}
  });
<% else %>
  layer.open({
    id: 'new_open',
    type: 1,
    title: '新增<%= WorkOrder.model_name.human %>',
    area: ['50%', '85%'],
    shade: 0.5,
    maxmin: true,
    offset: 'auto',
    content: "<%= escape_javascript(render 'form') %>",
    btn: ['保存', '关闭'],
    yes: function(){
      $('.save_btn').click();
    },
		success: function(layero, index){
			layer.full(index); // 最大化
		}
  });
<% end %>

