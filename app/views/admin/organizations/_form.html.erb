<%= simple_form_for([:admin, @organization], remote: true, html: {class: 'layui-form'}, wrapper: :seven_form_line) do |f| %>
  <%= f.error_notification %>

  <%= f.input :name %>
  <%= f.input :org_type %>
  <%= f.input :parent_id, collection: Organization.where(parent_id: nil).where.not(id: @organization.id), label_method: :name, value_method: :id, include_blank: true %>
  <% if params[:action] == 'new' %>
    <div class="layui-form-item">
      <%= label_tag "admin_user[name]", class: "layui-form-label" do %>
        <abbr title="required">*</abbr>
        管理员姓名
      <% end %>
      <div class="layui-input-block flex-item layui-input-wrap">
        <%= text_field_tag "admin_user[name]", "", class: "layui-input" %>
      </div>
    </div>

    <div class="layui-form-item">
      <%= label_tag "admin_user[phone]", class: "layui-form-label" do %>
        <abbr title="required">*</abbr>
        管理员手机
      <% end %>
      <div class="layui-input-block flex-item layui-input-wrap">
        <%= text_field_tag "admin_user[phone]", "", class: "layui-input" %>
      </div>
    </div>

    <div class="layui-form-item">
      <%= label_tag "admin_user[password]", class: "layui-form-label" do %>
        <abbr title="required">*</abbr>
        管理员密码
      <% end %>
      <div class="layui-input-block flex-item layui-input-wrap">
        <%= password_field_tag "admin_user[password]", "", class: "layui-input" %>
      </div>
    </div>
  <% end %>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>