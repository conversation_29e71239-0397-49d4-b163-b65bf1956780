<div style="padding: 20px;">
  <div class="layui-row layui-col-space16">
    <div class="layui-col-md6">
      <div class="layui-card">
        <div class="layui-card-header">可分配的模块</div>
        <div class="layui-card-body">
          <% if @available_permission_controllers.any? %>
            <table class="layui-table" lay-size="sm">
              <thead>
                <tr>
                  <th>控制器名称</th>
                  <th>控制器标识</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @available_permission_controllers.each do |controller| %>
                  <tr>
                    <td><%= controller.name %></td>
                    <td><%= controller.word %></td>
                    <td>
                      <% unless @organization.has_permission_controller?(controller.id) %>
                        <button class="layui-btn layui-btn-xs layui-btn-primary"
                                onclick="assignPermissionControllerInModal(<%= @organization.id %>, <%= controller.id %>)">
                          分配
                        </button>
                      <% else %>
                        <span class="layui-badge layui-bg-gray">已分配</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="layui-empty">
              <div class="layui-empty-icon">
                <i class="layui-icon layui-icon-face-cry"></i>
              </div>
              <p>暂无可分配的模块</p>
              <p class="layui-text">请联系父级企业管理员创建模块</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <div class="layui-col-md6">
      <div class="layui-card">
        <div class="layui-card-header">已分配的模块</div>
        <div class="layui-card-body">
          <% if @assigned_permission_controllers.any? %>
            <table class="layui-table" lay-size="sm">
              <thead>
                <tr>
                  <th>控制器名称</th>
                  <th>控制器标识</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @assigned_permission_controllers.each do |controller| %>
                  <tr>
                    <td><%= controller.name %></td>
                    <td><%= controller.word %></td>
                    <td>
                      <button class="layui-btn layui-btn-xs layui-btn-danger"
                              onclick="removePermissionControllerInModal(<%= @organization.id %>, <%= controller.id %>)">
                        移除
                      </button>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="layui-empty">
              <div class="layui-empty-icon">
                <i class="layui-icon layui-icon-face-surprised"></i>
              </div>
              <p>暂无已分配的模块</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
