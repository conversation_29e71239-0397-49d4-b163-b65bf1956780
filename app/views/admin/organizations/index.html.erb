<div class="layui-card">
  <%= simple_form_for @q, url: admin_organizations_path, method: :get, html: { class: 'layui-form layui-row layui-col-space16', style: 'padding: 20px 20px 0 20px' }, wrapper: :seven_form_line do |f| %>
    <div class="layui-col-md4">
      <%= f.input :name_cont, label: false, placeholder: '企业名称', input_html: { class: 'layui-input' }, required: false %>
    </div>
    <div class="layui-col-md4">
      <%= f.input :org_type_eq, label: false, placeholder: '请选择企业类型', input_html: { class: 'layui-input', layui_select: true, prompt: '请选择企业类型' }, collection: Organization.org_types_i18n.map { |k, v| [v, Organization.org_types[k]] }, required: false, include_blank: '全部' %>
    </div>
    <div class="layui-btn-container layui-col-xs12">
      <%= f.button :submit, '搜索', class: 'layui-btn', 'lay-submit': '', 'lay-filter': 'demo-table-search' %>
      <%= link_to '重置', admin_organizations_path, class: 'layui-btn layui-btn-primary' %>
    </div>
  <% end %>
  <div class="layui-card-body">
    <table id="table_organizations" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-blue layui-btn-primary" lay-event="permission_controllers" title="模块管理"> <i class="layui-icon layui-icon-set"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
</script>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer, //弹层
    laypage = layui.laypage, //分页
    page = 1,
    limit = 10

    table.render({
      id: 'listPage',
      elem: '#table_organizations',
      url: `/admin/organizations` + window.location.search, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      cols: [[

          {field: 'name', align:'center', title: '企业名称', minWidth: 100},

          {field: 'org_type', align:'center', title: '企业类型', minWidth: 100},

          {field: 'parent_id', align:'center', title: '父级企业', minWidth: 100},

          {field: 'assigned_permission_controllers_count', align:'center', title: '已分配模块数量', minWidth: 150},

        {fixed: 'right', title: '', minWidth: 250, align: 'center', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/organizations/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/organizations/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'permission_controllers'){
        $.ajax({
          type: 'GET',
          url: `/admin/organizations/${data.id}/permission_controllers`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/organizations/${data.id}`,
            data: {
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/organizations/new`,
            data: {
            }
          })
        break;
      };
    });

  })
</script>