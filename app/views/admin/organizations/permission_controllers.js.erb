layer.open({
  title: '<%= @organization.name %> - 模块权限分配',
  type: 1,
  area: ['70%', '60%'],
  content: `<div id='permission_controllers_id'><%= j render 'admin/organizations/permission_controllers' %></div>`,
  btn: ['关闭'],
  yes: function(index, layero){
    layer.close(index);
    // 刷新组织列表
    if (typeof table !== 'undefined') {
      table.reload('listPage');
    }
  }
});

// 在弹窗中分配模块
function assignPermissionControllerInModal(organizationId, permissionControllerId) {
  layer.confirm('确认分配此模块吗？', function(index){
    layer.close(index);
    $.ajax({
      type: 'POST',
      url: `/admin/organizations/${organizationId}/assign_permission_controller`,
      // dataType: 'json',
      data: {
        permission_controller_id: permissionControllerId
      },
      // success: function(response) {
        // if (response.status) {
          // layer.msg(response.msg || '分配成功', {icon: 1});
          // 重新加载模块管理弹窗
          // setTimeout(function() {
            // layer.closeAll();
            // $.ajax({
              // type: 'GET',
              // url: `/admin/organizations/${organizationId}/permission_controllers`
            // });
          // }, 1000);
        // } else {
          // layer.msg(response.msg || '分配失败', {icon: 2});
        // }
      // },
      // error: function() {
        // layer.msg('操作失败', {icon: 2});
      // }
    });
  });
}

// 在弹窗中移除模块
function removePermissionControllerInModal(organizationId, permissionControllerId) {
  layer.confirm('确认移除此模块吗？', function(index){
    layer.close(index);
    $.ajax({
      type: 'DELETE',
      url: `/admin/organizations/${organizationId}/remove_permission_controller`,
      data: {
        permission_controller_id: permissionControllerId
      }
    });
  });
}
