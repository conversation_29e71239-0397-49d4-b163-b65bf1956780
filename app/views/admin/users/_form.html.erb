<%= simple_form_for([:admin, @user], remote: true, wrapper: :seven_form_line, html: {id: 'user_form', class: 'layui-form', multipart: true,novalidate:'novalidate' }) do |f| %>
  <%= f.input :avatar, label: '头像', wrapper_html: {style: 'display: none'} %>
  <div class="layui-input-block">
    <button type="button" class="layui-btn" id="upload-img-file">
      <i class="layui-icon layui-icon-upload"></i> 上传用户头像
    </button>
    <div style="width: 132px;">
      <div class="layui-upload-list">
        <img class="layui-upload-img" <%= f.object&.avatar.present? ? "src=#{f.object&.avatar }" : '' %> id="ID-upload-demo-img" style="width: 100%; height: 132px;">
        <div id="ID-upload-demo-text"></div>
      </div>
      <div class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="filter-demo">
        <div class="layui-progress-bar" lay-percent=""></div>
      </div>
    </div>
  </div>

  <%= f.input :name, label: '名称', input_html: { 'lay-verify': "required" } %>
  <%= f.input :phone, label: '手机号', input_html: { 'lay-verify': "required" } %>
  <%= f.input :password, label: '密码' %>
  <%= f.input :password_confirmation, label: '确认密码' %>
  <%= f.input :organization_id, label: '所属组织', wrapper_html: { 'style': 'display: block' } %>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">权限</label>
      <div class="layui-input-block flex-item">
        <div id="select_role" style="width: 99%;" data-value="<%= current_user.organization.roles.map { |role| {name: role.name, value: role.id} }.to_json %>" data-selected="<%= @user.roles.pluck(:id).to_json %>"></div>
      </div>
    </div>
  </div>

  <div class="actions" style="display: none;">
    <%= f.submit '保存', data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': true %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form; //保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      var upload = layui.upload;
      var element = layui.element;
      form.render();

      var uploadInst = upload.render({
        elem: '#upload-img-file',
        url: '/upload_image', // 实际使用时改成您自己的上传接口即可。
        before: function(obj){
          // 预读本地文件示例，不支持ie8
          obj.preview(function(index, file, result){
            $('#ID-upload-demo-img').attr('src', result); // 图片链接（base64）
          });

          element.progress('filter-demo', '0%'); // 进度条复位
          layer.msg('上传中', {icon: 16, time: 0});
        },
        done: function(res){
          // 若上传失败
          if(res.code > 0){
            return layer.msg('上传失败');
          }
          // 上传成功的一些操作
          $('#user_avatar').val(res.link)
          $('#ID-upload-demo-text').html(''); // 置空上传失败的状态
        },
        error: function(){
          // 演示失败状态，并实现重传
          var demoText = $('#ID-upload-demo-text');
          demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
          demoText.find('.demo-reload').on('click', function(){
            uploadInst.upload();
          });
        },
        // 进度条
        progress: function(n, elem, e){
          element.progress('filter-demo', n + '%'); // 可配合 layui 进度条元素使用
          if(n == 100){
            layer.msg('上传完毕', {icon: 1});
          }
        }
      });
    });
  });

  var select_role;
  layui.use(['xmSelect'], function () {
    var xmSelect = layui.xmSelect
    //渲染数据
    select_role = xmSelect.render({
      el: '#select_role',
      autoRow: true,
      toolbar: { show: false },
      tips: '请选择权限',
      filterable: false,
      data: JSON.parse($('#select_role').attr('data-value')),
      initValue: JSON.parse($('#select_role').attr('data-selected'))
    })
    select_role.getValue();
  })
</script>
