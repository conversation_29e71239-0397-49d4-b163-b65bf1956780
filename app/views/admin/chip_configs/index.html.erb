<div class="layui-card">
  <div class="layui-card-body">
    <table id="table_chip_configs" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  <!-- <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a> -->
</script>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_chip_configs',
      url: `/admin/chip_configs`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      defaultToolbar: [],
      cols: [[
        {field: 'name', align:'left', title: '芯片名称', minWidth: 100},
        {field: 'code', align:'left', title: '芯片编码', minWidth: 100},
        {field: 'c_type', align:'left', title: '芯片类型', minWidth: 100},
        {field: 'product_line', align:'left', title: '产品线', minWidth: 100},
        {field: 'status', align:'left', title: '状态', minWidth: 100},
        {field: 'product_category_id', align:'left', title: '产品分类', minWidth: 100},
        {field: 'description', align:'left', title: '描述', minWidth: 100},

        {fixed: 'right', title: '操作', minWidth: 170, align: 'left', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/chip_configs/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/chip_configs/${data.id}`,
            data: {
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/chip_configs/new`,
            data: {
            }
          })
        break;
      };
    });

  })
</script>