<%= simple_form_for([:admin, @chip_config], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>
    <div class="flex">
      <%= f.input :name, input_html: {'lay-verify': "required"}%>
      <%= f.input :code, input_html: {'lay-verify': "required"}%>
    </div>
    <div class="flex">
      <%= f.input :product_line, input_html: {'lay-verify': "required"}%>
      <%= f.input :c_type, input_html: {'lay-verify': "required"}%>
    </div>
    <%= f.input :product_category_id, collection: @product_category_array, input_html: {'lay-verify': "required"}%>
    <%= f.input :status, collection: [['启用', true], ['禁用', false]], input_html: {'lay-verify': "required"} %>
    <%= f.input :description, as: :text, input_html: {class: 'layui-textarea'}%>

    <%= f.input :organization_id, wrapper_html: {style: 'display: none'} %>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>