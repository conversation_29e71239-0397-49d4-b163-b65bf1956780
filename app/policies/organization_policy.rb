class OrganizationPolicy < ApplicationPolicy
  def index?
    check_auth("organizations", "index")
  end

  def create?
    check_auth("organizations", "create")
  end

  def update?
    check_auth("organizations", "update")
  end

  def destroy?
    check_auth("organizations", "destroy")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def show?
    check_auth("organizations", "index")
  end

  def permission_controllers?
    check_auth("organizations", "auth")
  end

  def assign_permission_controller?
    check_auth("organizations", "auth")
  end

  def remove_permission_controller?
    check_auth("organizations", "auth")
  end
end
