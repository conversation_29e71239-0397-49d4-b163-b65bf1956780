class ProjectPolicy < ApplicationPolicy
  def index?
    check_auth("projects", "index")
  end

  def create?
    check_auth("projects", "create")
  end

  def update?
    check_auth("projects", "update")
  end

  def destroy?
    check_auth("projects", "destroy")
  end

  def new?
    create?
  end

  def new_joint?
    create?
  end

  def new_support?
    create?
  end

  def edit?
    update?
  end

  def show?
    check_auth("projects", "show")
  end


  def edit_joint?
    update?
  end

  def edit_support?
    update?
  end

  def update_status?
    update?
  end

  def admin?
    check_auth("projects", "admin")
  end
end
