class ProductCategoryPolicy < ApplicationPolicy
  def index?
    check_auth("product_categories", "index")
  end

  def create?
    check_auth("product_categories", "create")
  end

  def update?
    check_auth("product_categories", "update")
  end

  def destroy?
    check_auth("product_categories", "destroy")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def show?
    check_auth("product_categories", "index")
  end
end
