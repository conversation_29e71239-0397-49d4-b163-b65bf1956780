

class FlowVersionPolicy < ApplicationPolicy

  # 继承主流程配置权限
  def index?
    check_auth("organization_flows", "index")
  end

  def show?
    check_auth("organization_flows", "index")
  end

  def create?
    check_auth("organization_flows", "create")
  end

  def update?
    check_auth("organization_flows", "update")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def destroy?
    true
  end

end