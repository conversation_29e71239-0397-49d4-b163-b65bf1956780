
  
class WorkOrderPolicy < ApplicationPolicy
    
  def index?
    project_role_configs = user.project_role_configs
    ProjectPermissionConfig.where(id:  ProjectRolePermission.where(project_role_config_id: project_role_configs.select(:id)).select(:project_permission_config_id).distinct, key: 'view_work_orders').present?
  end

  def show?  
    true
  end  

  def create?
    project_role_configs = user.project_role_configs
    ProjectPermissionConfig.where(id:  ProjectRolePermission.where(project_role_config_id: project_role_configs.select(:id)).select(:project_permission_config_id).distinct, key: 'new_work_orders').present?
  end  

  def new?  
    create?  
  end  

  def update?  
    true
  end  

  def edit?  
    update?  
  end  

  def destroy?  
    true
  end

  def evaluate_work_orders?
    true
  end

  def passing_work_orders?
    true
  end

  def change_state?
    true
  end

  def project_demand_works?
    true
  end

  def project_bug_works?
    true
  end

  
end  