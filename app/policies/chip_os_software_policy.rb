class ChipOsSoftwarePolicy < ApplicationPolicy
  def index?
    check_auth("chip_os_softwares", "index")
  end

  def create?
    check_auth("chip_os_softwares", "create")
  end

  def update?
    check_auth("chip_os_softwares", "update")
  end

  def destroy?
    check_auth("chip_os_softwares", "destroy")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def show?
    check_auth("chip_os_softwares", "index")
  end

  def get_version?
    true
  end
end
