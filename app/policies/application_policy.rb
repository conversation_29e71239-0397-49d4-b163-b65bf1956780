# frozen_string_literal: true

class ApplicationPolicy
  attr_reader :user, :record

  def initialize(user, record)
    @user = user
    @record = record
  end

  def index?
    false
  end

  def show?
    false
  end

  def create?
    false
  end

  def new?
    create?
  end

  def update?
    false
  end

  def edit?
    update?
  end

  def destroy?
    false
  end

  class Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      raise NoMethodError, "You must define #resolve in #{self.class}"
    end

    private

    attr_reader :user, :scope
  end

  private

  def pundit_user
    current_admin_user
  end

  def check_auth(visit_controller, visit_action, visit_user=user)
    if visit_user.is_admin?
      if visit_user.organization_id == Settings.main_organization_id
        return true
      else
        available_permission_controller_ids = visit_user.organization.organization_permission_controllers.pluck(:permission_controller_id)
        available_words = PermissionController.where(id: available_permission_controller_ids).pluck(:word)
        if available_words.include?(visit_controller)
          return true
        else
          return false
        end
      end
    end

    permission_action = visit_user.organization.permission_actions.find_by(
      word: visit_action,
      permission_controller: visit_user.organization.permission_controllers.find_by(word: visit_controller)
    )

    return false if permission_action.blank?

    if visit_user.roles.any? { |role|
      visit_user.organization.role_permissions.where(
        role_id: role.id,
        permission_action_id: permission_action.id
      ).present?
    }
      return true
    else
      return false
    end
  end
end
