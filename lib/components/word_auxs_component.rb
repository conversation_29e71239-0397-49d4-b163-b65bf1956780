module WordAuxsComponent
  # To avoid deprecation warning, you need to make the wrapper_options explicit
  # even when they won't be used.
  def word_aux(wrapper_options = nil)
    @word_aux ||= begin
      options[:word_aux].to_s.html_safe if options[:word_aux].present?
    end
  end

  def text_aux(wrapper_options = nil)
    @text_aux ||= begin
      options[:text_aux].to_s.html_safe if options[:text_aux].present?
    end
  end

  def textunit(wrapper_options = nil)
    options[:textunit].to_s.html_safe if options[:textunit].present?
  end

  def text_content(wrapper_options = nil)
    options[:text_content].to_s.html_safe if options[:text_content].present?
  end
end

SimpleForm.include_component(WordAuxsComponent)