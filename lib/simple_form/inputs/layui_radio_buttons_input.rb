class LayuiRadioButtonsInput < SimpleForm::Inputs::CollectionRadioButtonsInput
  def input(wrapper_options = nil)
    label_method, value_method = detect_collection_methods

    # Your custom code to generate the layui radio buttons HTML
    collection.map do |option|
      label_method, value_method = detect_collection_methods

      label = option_label(option)
      value = option_value(option)

      label_method, value_method = detect_collection_methods

    input_options[:item_wrapper_class] ||= 'radio'
    input_options[:item_label_class] ||= 'radio-label'
    input_options[:item_input_class] ||= 'radio-input'

    input_html_options = merge_wrapper_options(input_html_options, wrapper_options)


      input_html_options = input_html_options(option)
      merged_input_options = merge_wrapper_options(input_html_options, wrapper_options)

      label_options = label_html_options.dup

      label_options[:for] ||= input_html_options[:id] if label_required?

      label_options[:class] ||= []
      label_options[:class] += input_options.fetch(:label_class, [])

      label_options[:class].reject!(&:blank?)

      label_html = template.label_tag(nil, label_options) do
        template.radio_button_tag(attribute_name, value, input_html_options.merge(checked: checked?(value)), data: { layui: 'radio' }) + label
      end

      label_html.concat("\n")
    end.join.html_safe
  end
end