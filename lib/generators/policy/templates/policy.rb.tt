<%# 头部注释 - 如果指定了作者或者timestamp则生成注释 %>
<% if <AUTHOR> @with_timestamp -%>
# -------------------------------------------------------------
# Policy for <%= class_name %>
<% if <AUTHOR>
# Author: <%= <AUTHOR>
<% end -%>
<% if @with_timestamp -%>
# Generated at: <%= Time.now.strftime('%Y-%m-%d %H:%M:%S') %>
<% end -%>
# -------------------------------------------------------------
<% end -%>  
class <%= class_name %>Policy < <%= @parent_class %>
  <% if @gen_initialize -%>
  attr_reader :user, :<%= file_name %>
  def initialize(user, <%= file_name %>)
      @user = user
      @<%= file_name %> = <%= file_name %>
    end
  <% end -%>
<% if @with_crud -%>  
  def index?  
    true
  end  

  def show?  
    true
  end  

  def create?  
    true
  end  

  def new?  
    create?  
  end  

  def update?  
    true
  end  

  def edit?  
    update?  
  end  

  def destroy?  
    true
  end  
<% else -%>  
  # 在此处定义您的授权方法
<% end -%>  
end  