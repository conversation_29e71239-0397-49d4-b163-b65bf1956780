# lib/generators/policy/policy_generator.rb
require 'rails/generators'
require 'rails/generators/named_base'

class PolicyGenerator < Rails::Generators::NamedBase
  source_root File.expand_path('templates', __dir__)

  # 添加选项
  class_option :parent, type: :string, default: 'ApplicationPolicy', desc: "继承的父类"
  class_option :author, type: :string, default: nil, desc: "作者"
  class_option :crud, type: :boolean, default: true, desc: "生成 CRUD 方法 (index, show, create, update, destroy)"
  class_option :timestamp, type: :boolean, default: false, desc: "文件生成时间"

  #rails generate policy WorkOrder
  #rails generate policy Comment --author="ning" --timestamp=false --crud=true
  #rails generate policy WorkOrder --parent="BasePolicy"
  def create_policy_file
    @parent_class = options[:parent]
    <AUTHOR> options[:author]
    @with_crud = options[:crud]
    @with_timestamp = options[:timestamp]
    @gen_initialize = false
    template 'policy.rb.tt', File.join('app/policies', class_path, "#{file_name}_policy.rb")
  end
end