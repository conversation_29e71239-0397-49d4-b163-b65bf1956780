<div class="panel-default">
  <table lay-filter="demo" lay-size='lg'>
    <thead>
      <tr>
        <th lay-data="{field:'attr'}" style="width:30%;">属性</th>
        <th lay-data="{field:'attr_value'}">值</th>
      </tr>
    </thead>

    <tbody>
  <% attributes.reject(&:password_digest?).each do |attribute| %>
    <tr>
      <td><%%= <%= singular_table_name.classify %>.human_attribute_name(:<%= attribute.name %>) %>:</td>
      <td><%%= @<%= singular_table_name %>.<%= attribute.name %> %></td>
    </tr>
  <% end %>
    </tbody>
  </table>

  <div class="actions_show">
    <%%= link_to t('buttons.edit'), edit_admin_<%= singular_table_name %>_path(@<%= singular_table_name %>), class: 'layui-btn' %>
    <%%= link_to t('buttons.back'), admin_<%= index_helper %>_path, class: 'layui-btn layui-btn-normal' %>
  </div>
</div>